/**
 * Test script to debug debt creation issues
 * This will help identify why "Database operation failed" occurs
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseKey ? 'Set' : 'Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTableStructure() {
  console.log('🔍 Checking customer_debts table structure...');
  
  try {
    // Check if table exists by trying to select from it
    const { data, error } = await supabase
      .from('customer_debts')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Table check failed:', error.message);
      return false;
    }
    
    console.log('✅ Table exists and is accessible');
    console.log('📊 Sample data structure:', data);
    return true;
  } catch (err) {
    console.error('❌ Unexpected error:', err.message);
    return false;
  }
}

async function testDebtCreation() {
  console.log('\n🧪 Testing debt creation...');
  
  const testDebt = {
    customer_name: 'Dave',
    customer_family_name: 'Mejos',
    product_name: 'halik sa langit',
    product_price: 23.00,
    quantity: 3,
    debt_date: new Date().toISOString().split('T')[0],
    notes: 'Test debt - safe to delete'
  };
  
  console.log('📝 Test data:', testDebt);
  
  try {
    const { data, error } = await supabase
      .from('customer_debts')
      .insert([testDebt])
      .select()
      .single();
    
    if (error) {
      console.error('❌ Insert failed:', error);
      console.error('Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      return null;
    }
    
    console.log('✅ Debt created successfully:', data);
    return data;
  } catch (err) {
    console.error('❌ Unexpected error during insert:', err.message);
    return null;
  }
}

async function testAPIEndpoint() {
  console.log('\n🌐 Testing API endpoint...');

  const testData = {
    customer_name: 'Dave',
    customer_family_name: 'Mejos',
    product_name: 'halik sa langit',
    product_price: 23.00,
    quantity: 3,
    debt_date: new Date().toISOString().split('T')[0],
    notes: 'API test - safe to delete'
  };

  console.log('📝 Sending data:', JSON.stringify(testData, null, 2));

  try {
    const response = await fetch('http://localhost:3001/api/debts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('📡 Raw response body:', responseText);

    // Try to parse as JSON
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
      console.log('📡 Parsed response:', JSON.stringify(parsedResponse, null, 2));
    } catch (parseErr) {
      console.error('❌ Failed to parse response as JSON:', parseErr.message);
      console.log('📡 Response is not valid JSON');
    }

    if (!response.ok) {
      console.error('❌ API request failed with status:', response.status);
      return null;
    }

    console.log('✅ API request successful:', parsedResponse);
    return parsedResponse;
  } catch (err) {
    console.error('❌ API request error:', err.message);
    console.error('❌ Full error:', err);
    return null;
  }
}

async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    const { error } = await supabase
      .from('customer_debts')
      .delete()
      .or('notes.ilike.%Test debt%,notes.ilike.%API test%');
    
    if (error) {
      console.error('❌ Cleanup failed:', error.message);
    } else {
      console.log('✅ Test data cleaned up');
    }
  } catch (err) {
    console.error('❌ Cleanup error:', err.message);
  }
}

async function main() {
  console.log('🚀 Starting debt creation debugging...\n');
  
  // Check table structure
  const tableExists = await checkTableStructure();
  if (!tableExists) {
    console.log('❌ Cannot proceed - table issues detected');
    return;
  }
  
  // Test direct database insertion
  const directResult = await testDebtCreation();
  
  // Test API endpoint
  const apiResult = await testAPIEndpoint();
  
  // Clean up
  await cleanupTestData();
  
  console.log('\n📋 Summary:');
  console.log('Direct DB insert:', directResult ? '✅ Success' : '❌ Failed');
  console.log('API endpoint:', apiResult ? '✅ Success' : '❌ Failed');
  
  if (!directResult && !apiResult) {
    console.log('\n💡 Both direct DB and API failed - likely a table structure issue');
  } else if (directResult && !apiResult) {
    console.log('\n💡 Direct DB works but API fails - likely an API validation issue');
  } else if (!directResult && apiResult) {
    console.log('\n💡 API works but direct DB fails - unexpected scenario');
  } else {
    console.log('\n💡 Both work - the issue might be intermittent or environment-specific');
  }
}

main().catch(console.error);
