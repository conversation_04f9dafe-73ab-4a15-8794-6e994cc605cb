// Shared TypeScript types and interfaces for the application

// Import database types
import type { Product, Customer, CustomerDebt, CustomerPayment, CustomerBalance, PaymentMethod } from '@/lib/supabase'

// Re-export database types
export type { Product, Customer, CustomerDebt, CustomerPayment, CustomerBalance, PaymentMethod }

// Common UI component props
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// Modal component props
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
}

// Loading states
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Form validation states
export interface FormState<T = unknown> extends LoadingState {
  data: T | null
  isDirty: boolean
  isValid: boolean
}

// Dashboard statistics
export interface DashboardStats {
  totalProducts: number
  lowStockItems: number
  recentProducts: Product[]
}

// Navigation items
export interface NavigationItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  tooltip?: string
  badge?: number
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system'

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Pagination types
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Search and filter types
export interface SearchFilters {
  query?: string
  category?: string
  dateFrom?: string
  dateTo?: string
  status?: string
}

// Component size variants
export type ComponentSize = 'sm' | 'md' | 'lg' | 'xl'
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error'

// Event handler types
export type EventHandler<T = Event> = (event: T) => void
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>

// Database error types
export interface DatabaseError extends Error {
  code?: string
  hint?: string
  details?: string
  message: string
}

// API error types
export interface ApiErrorResponse {
  message: string
  code?: string
  details?: string
  stack?: string
}

// Generic error handler type
export type ErrorHandler = (error: DatabaseError | Error | unknown) => void

// Form event types
export type FormSubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void
export type InputChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void
export type SelectChangeHandler = (event: React.ChangeEvent<HTMLSelectElement>) => void
export type TextAreaChangeHandler = (event: React.ChangeEvent<HTMLTextAreaElement>) => void

// API response data types
export interface ProductsApiResponse {
  products: Product[]
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface CustomersApiResponse {
  customers: Customer[]
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface DebtsApiResponse {
  debts: CustomerDebt[]
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface PaymentsApiResponse {
  payments: CustomerPayment[]
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface CustomerBalancesApiResponse {
  balances: CustomerBalance[]
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
