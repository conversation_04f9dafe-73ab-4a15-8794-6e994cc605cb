import { v2 as cloudinary } from 'cloudinary'
import { NextRequest, NextResponse } from 'next/server'

import { logError, logApi } from '@/lib/logger'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
})

export async function POST(request: NextRequest) {
  try {
    // Check Cloudinary configuration first
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      logError('Missing Cloudinary configuration', undefined, {
        cloudName: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        apiKey: !!process.env.CLOUDINARY_API_KEY,
        apiSecret: !!process.env.CLOUDINARY_API_SECRET
      })
      return NextResponse.json({ error: 'Server configuration error: Missing Cloudinary credentials' }, { status: 500 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const oldPublicId = formData.get('old_public_id') as string | null

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique public_id for Cloudinary
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const publicId = `sari-sari-products/product_${timestamp}_${randomString}`

    logApi('POST', '/api/upload', undefined, undefined)

    // Upload to Cloudinary with timeout and retry logic
    const uploadWithRetry = async (retryCount = 0): Promise<{ secure_url: string; public_id: string }> => {
      const maxRetries = 2
      const timeout = 30000 // 30 seconds timeout

      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Upload timeout - please try with a smaller image or check your internet connection'))
        }, timeout)

        const uploadStream = cloudinary.uploader.upload_stream(
          {
            resource_type: 'image',
            public_id: publicId,
            folder: 'sari-sari-products',
            transformation: [
              { width: 400, height: 400, crop: 'limit' }, // Reduced size for faster upload
              { quality: 'auto:good' }, // Reduced quality for faster upload
              { format: 'auto' }
            ],
            timeout: 25000 // Cloudinary timeout
          },
          async (error, result) => {
            clearTimeout(timeoutId)

            if (error) {
              logError(`Cloudinary upload error (attempt ${retryCount + 1})`, error)

              // Retry on timeout or network errors
              if ((error.message.includes('timeout') || error.message.includes('ECONNRESET') || error.message.includes('ETIMEDOUT')) && retryCount < maxRetries) {
                logApi('POST', `/api/upload/retry-${retryCount + 2}`, undefined, undefined)
                try {
                  const retryResult = await uploadWithRetry(retryCount + 1)
                  resolve(retryResult)
                } catch (retryError) {
                  reject(retryError)
                }
              } else {
                reject(new Error(`Cloudinary upload failed: ${error.message}`))
              }
            } else if (result) {
              logApi('POST', '/api/upload', 200, undefined)
              resolve({ secure_url: result.secure_url, public_id: result.public_id })
            } else {
              reject(new Error('Upload failed: No result returned from Cloudinary'))
            }
          }
        )

        uploadStream.end(buffer)
      })
    }

    const result = await uploadWithRetry()

    // Delete old image from Cloudinary if it exists
    if (oldPublicId) {
      try {
        logApi('DELETE', `/cloudinary/${oldPublicId}`, undefined, undefined)
        const deleteResult = await cloudinary.uploader.destroy(oldPublicId)

        if (deleteResult.result === 'ok') {
          logApi('DELETE', `/cloudinary/${oldPublicId}`, 200, undefined)
        } else if (deleteResult.result === 'not found') {
          logApi('DELETE', `/cloudinary/${oldPublicId}`, 404, undefined)
        } else {
          logError(`Failed to delete old product image: ${oldPublicId}`, deleteResult)
        }
      } catch (deleteError) {
        // Don't fail the upload if deletion fails - just log the error
        logError(`Error deleting old product image ${oldPublicId}`, deleteError)
      }
    }

    // Add cache-busting parameter to ensure fresh image loads
    const cacheBustUrl = `${result.secure_url}?cache_bust=${Date.now()}`

    return NextResponse.json({
      success: true,
      url: cacheBustUrl,
      public_id: result.public_id
    })
  } catch (error) {
    logError('Upload API error', error)

    // Provide more specific error messages
    let errorMessage = 'Failed to upload image'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: error instanceof Error ? error.stack : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE - Remove product image from Cloudinary
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 })
    }

    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId)

    if (result.result === 'ok') {
      return NextResponse.json({
        success: true,
        message: 'Product image deleted successfully from cloud storage'
      })
    } else {
      return NextResponse.json({
        error: 'Failed to delete image from cloud storage'
      }, { status: 400 })
    }

  } catch (error) {
    logError('Error deleting product image from Cloudinary', error)
    return NextResponse.json(
      { error: 'Failed to delete image from cloud storage' },
      { status: 500 }
    )
  }
}
