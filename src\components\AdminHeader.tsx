'use client'

import { Search, Home, Package, Moon, Sun, LogOut, User, CreditCard, Images } from 'lucide-react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { useAuth } from '@/contexts/AuthContext'

interface AdminHeaderProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

export default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const { setTheme, resolvedTheme } = useTheme()
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { user, logout } = useAuth()

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])



  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Home Dashboard',
      icon: Home,
      tooltip: 'Dashboard Overview'
    },
    {
      id: 'products',
      label: 'Product Lists',
      icon: Package,
      tooltip: 'Manage Products'
    },
    {
      id: 'debts',
      label: 'Debt Management',
      icon: CreditCard,
      tooltip: 'Customer Debt (Utang) Management'
    },
    {
      id: 'gallery',
      label: 'Gallery',
      icon: Images,
      tooltip: 'Family Gallery & Store Memories'
    },
  ]

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement search functionality
    // TODO: Add search functionality
  }

  const toggleTheme = () => {
    if (!mounted) return

    // Manual DOM manipulation for immediate visual feedback
    const html = document.documentElement
    const isDark = resolvedTheme === 'dark'

    if (isDark) {
      html.classList.remove('dark')
      setTheme('light')
    } else {
      html.classList.add('dark')
      setTheme('dark')
    }
  }

  const handleLogout = () => {
    logout()
    window.location.href = '/login'
  }

  return (
    <header className="admin-header fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300" style={{
      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
    }}>
      <div className="grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4">
        
        {/* Left Section - Logo & Search (Fixed Width) */}
        <div className="flex items-center space-x-3 w-auto">
          {/* Revantad Logo */}
          <Link
            href="/landing"
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0"
            title="Return to Front Page"
          >
            <div className="w-10 h-10 hero-gradient rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-lg">R</span>
            </div>
            <span className="text-xl font-bold text-gradient-enhanced hidden sm:block">Revantad</span>
          </Link>

          {/* Search Bar - Much Shorter than Sidebar (320px) */}
          <form onSubmit={handleSearch} className="w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="header-search"
                name="search"
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200"
                autoComplete="off"
              />
            </div>
          </form>
        </div>

        {/* Center Section - Navigation Icons (Facebook-style) */}
        <div className="hidden sm:flex items-center justify-center">
          <div className="flex items-center space-x-3 md:space-x-4 lg:space-x-5">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = activeSection === item.id

              return (
                <button
                  key={item.id}
                  onClick={() => {
                    setActiveSection(item.id)
                  }}
                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-300 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.08] hover:shadow-lg`}
                  style={{
                    background: isActive
                      ? (resolvedTheme === 'dark'
                          ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.25) 0%, rgba(16, 185, 129, 0.2) 100%)'
                          : 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(16, 185, 129, 0.1) 100%)')
                      : 'transparent',
                    color: isActive
                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),
                    boxShadow: isActive
                      ? (resolvedTheme === 'dark'
                          ? '0 4px 12px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                          : '0 4px 12px rgba(34, 197, 94, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.8)')
                      : 'none',
                    border: isActive
                      ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')
                      : '1px solid transparent'
                  }}
                  title={item.tooltip}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = resolvedTheme === 'dark'
                        ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%)'
                        : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'
                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                      e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                        ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                        : '0 2px 8px rgba(0, 0, 0, 0.1)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = 'transparent'
                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'
                      e.currentTarget.style.boxShadow = 'none'
                    }
                  }}
                >
                  {/* Active indicator - Enhanced */}
                  {isActive && (
                    <>
                      <div
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-12 md:w-14 lg:w-16 h-1.5 rounded-full transition-all duration-300"
                        style={{
                          background: resolvedTheme === 'dark'
                            ? 'linear-gradient(90deg, #4ade80 0%, #22c55e 100%)'
                            : 'linear-gradient(90deg, #16a34a 0%, #22c55e 100%)',
                          boxShadow: '0 2px 6px rgba(34, 197, 94, 0.5)'
                        }}
                      />
                      {/* Glow effect */}
                      <div
                        className="absolute inset-0 rounded-xl opacity-20 transition-all duration-300"
                        style={{
                          background: resolvedTheme === 'dark'
                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%)'
                            : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%)',
                          filter: 'blur(1px)'
                        }}
                      />
                    </>
                  )}

                  <Icon className="h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-300 group-hover:scale-110 relative z-10" />

                  {/* Enhanced Professional Tooltip */}
                  <div className="absolute top-full mt-4 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-gray-800 text-white dark:text-white text-xs px-4 py-3 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50 shadow-xl border border-gray-700">
                    <div className="font-semibold text-white dark:text-white">{item.label}</div>
                    <div className="text-gray-300 dark:text-gray-300 text-[10px] mt-1">{item.tooltip}</div>
                    {/* Tooltip arrow */}
                    <div className="absolute -top-1.5 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-gray-900 dark:bg-gray-800 rotate-45 border-l border-t border-gray-700"></div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Mobile Navigation - Simplified */}
        <div className="sm:hidden flex items-center justify-center space-x-2">
          <button
            onClick={() => setActiveSection('dashboard')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'dashboard'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Dashboard"
          >
            <Home className="h-5 w-5" />
          </button>
          <button
            onClick={() => setActiveSection('products')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'products'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Products"
          >
            <Package className="h-5 w-5" />
          </button>
          <button
            onClick={() => setActiveSection('debts')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'debts'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Debts"
          >
            <CreditCard className="h-5 w-5" />
          </button>
          <button
            onClick={() => setActiveSection('gallery')}
            className={`p-2 rounded-lg transition-colors ${
              activeSection === 'gallery'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
            title="Gallery"
          >
            <Images className="h-5 w-5" />
          </button>
        </div>

        {/* Right Section - Dark Mode & Profile */}
        <div className="flex items-center justify-end space-x-3">


          {/* Dark Mode Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0"
            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}
            disabled={!mounted}
          >
            {!mounted ? (
              <div className="h-5 w-5 bg-gray-400 rounded-full animate-pulse" />
            ) : resolvedTheme === 'dark' ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </button>

          {/* Profile Dropdown */}
          <div className="relative flex-shrink-0">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
                <User className="h-4 w-4 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors">
                {user?.name || 'Admin'}
              </span>
            </button>

            {/* Dropdown Menu */}
            {isProfileOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1">
                <div className="px-4 py-2 border-b border-gray-200 dark:border-slate-700">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email || '<EMAIL>'}</p>
                </div>
                
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
