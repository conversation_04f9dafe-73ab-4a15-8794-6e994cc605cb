'use client'

import { useState, useEffect, useRef } from 'react'
import { useTheme } from 'next-themes'
import Image from 'next/image'
import {
  Upload,
  X,
  Heart,
  Eye,
  Calendar,
  FileImage,
  Plus,
  Search,
  Grid3X3,
  List,
  Trash2,
  MoreVertical
} from 'lucide-react'

import { logError } from '@/lib/logger'

interface GalleryPhoto {
  id: string
  title: string
  description?: string
  image_url: string
  image_public_id: string
  file_size?: number
  image_width?: number
  image_height?: number
  likes_count: number
  upload_date: string
  created_at: string
  updated_at: string
}

interface UploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUpload: (photo: GalleryPhoto) => void
}

function UploadModal({ isOpen, onClose, onUpload }: UploadModalProps) {
  const { resolvedTheme } = useTheme()
  const [isUploading, setIsUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file)
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0 && files[0]) {
      handleFileSelect(files[0])
    }
  }

  const handleUpload = async () => {
    if (!selectedFile || !title.trim()) return

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('title', title.trim())
      if (description.trim()) {
        formData.append('description', description.trim())
      }

      const response = await fetch('/api/gallery', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const data = await response.json()
      onUpload(data.photo)
      
      // Reset form
      setTitle('')
      setDescription('')
      setSelectedFile(null)
      setPreviewUrl(null)
      onClose()
    } catch (error) {
      logError('Upload error', error)
      alert(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  const resetForm = () => {
    setTitle('')
    setDescription('')
    setSelectedFile(null)
    setPreviewUrl(null)
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
    }
  }

  useEffect(() => {
    if (!isOpen) {
      resetForm()
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div 
        className="bg-white dark:bg-slate-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Upload Photo to Gallery
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
              dragOver
                ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                : 'border-gray-300 dark:border-slate-600'
            }`}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault()
              setDragOver(true)
            }}
            onDragLeave={() => setDragOver(false)}
          >
            {previewUrl ? (
              <div className="space-y-4">
                <div className="relative w-full h-48 rounded-lg overflow-hidden">
                  <Image
                    src={previewUrl}
                    alt="Preview"
                    fill
                    className="object-cover"
                  />
                </div>
                <button
                  onClick={() => {
                    setSelectedFile(null)
                    setPreviewUrl(null)
                  }}
                  className="text-red-600 hover:text-red-700 text-sm"
                >
                  Remove Image
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <FileImage className="h-12 w-12 text-gray-400 mx-auto" />
                <div>
                  <p className="text-lg font-medium text-gray-900 dark:text-white">
                    Drop your image here or click to browse
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Supports JPEG, PNG, WebP, GIF (max 10MB)
                  </p>
                </div>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Choose File
                </button>
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) handleFileSelect(file)
            }}
            className="hidden"
          />

          {/* Form Fields */}
          <div className="space-y-4 mt-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter photo title..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 dark:text-white"
                maxLength={255}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Add a description for this photo..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 dark:text-white resize-none"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleUpload}
              disabled={!selectedFile || !title.trim() || isUploading}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isUploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  <span>Upload Photo</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function Gallery() {
  const { resolvedTheme } = useTheme()
  const [photos, setPhotos] = useState<GalleryPhoto[]>([])
  const [loading, setLoading] = useState(true)
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<GalleryPhoto | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [likedPhotos, setLikedPhotos] = useState<Set<string>>(new Set())

  // Load photos on component mount
  useEffect(() => {
    loadPhotos()
  }, [])

  const loadPhotos = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/gallery')
      if (!response.ok) {
        throw new Error('Failed to load photos')
      }
      const data = await response.json()
      setPhotos(data.photos || [])
    } catch (error) {
      logError('Failed to load gallery photos', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePhotoUpload = (newPhoto: GalleryPhoto) => {
    setPhotos(prev => [newPhoto, ...prev])
  }

  const handleLike = async (photoId: string) => {
    try {
      const response = await fetch('/api/gallery/like', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ photoId }),
      })

      if (!response.ok) {
        throw new Error('Failed to toggle like')
      }

      const data = await response.json()

      // Update photos state
      setPhotos(prev => prev.map(photo =>
        photo.id === photoId
          ? { ...photo, likes_count: data.likesCount }
          : photo
      ))

      // Update liked photos set
      setLikedPhotos(prev => {
        const newSet = new Set(prev)
        if (data.isLiked) {
          newSet.add(photoId)
        } else {
          newSet.delete(photoId)
        }
        return newSet
      })
    } catch (error) {
      logError('Failed to toggle like', error)
    }
  }

  const handleDeletePhoto = async (photoId: string) => {
    if (!confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
      return
    }

    try {
      console.log('Deleting photo with ID:', photoId)

      const response = await fetch(`/api/gallery?id=${encodeURIComponent(photoId)}`, {
        method: 'DELETE',
      })

      console.log('Delete response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        console.error('Delete failed with error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete photo`)
      }

      const result = await response.json()
      console.log('Delete successful:', result)

      // Remove photo from state
      setPhotos(prev => prev.filter(photo => photo.id !== photoId))

      // Remove from liked photos if it was liked
      setLikedPhotos(prev => {
        const newSet = new Set(prev)
        newSet.delete(photoId)
        return newSet
      })

      // Close modal if this photo was being viewed
      if (selectedPhoto?.id === photoId) {
        setSelectedPhoto(null)
      }

      alert('Photo deleted successfully!')

    } catch (error) {
      console.error('Delete photo error:', error)
      logError('Failed to delete photo', error)
      alert(`Failed to delete photo: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const filteredPhotos = photos.filter(photo =>
    photo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (photo.description && photo.description.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-4 border-green-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Family Gallery
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Store memories and special moments from Revantad Store
          </p>
        </div>
        
        <button
          onClick={() => setIsUploadModalOpen(true)}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Upload Photo
        </button>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search photos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 dark:text-white"
            />
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
          >
            <Grid3X3 className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list'
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
            }`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Photos Grid/List */}
      {filteredPhotos.length === 0 ? (
        <div className="text-center py-12">
          <FileImage className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {searchQuery ? 'No photos found' : 'No photos yet'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchQuery 
              ? 'Try adjusting your search terms'
              : 'Start building your gallery by uploading your first photo'
            }
          </p>
          {!searchQuery && (
            <button
              onClick={() => setIsUploadModalOpen(true)}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Upload First Photo
            </button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {filteredPhotos.map((photo) => (
            <div
              key={photo.id}
              className={`group relative bg-white dark:bg-slate-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden ${
                viewMode === 'list' ? 'flex items-center p-4' : ''
              }`}
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
              }}
            >
              <div
                className={`relative overflow-hidden cursor-pointer ${
                  viewMode === 'grid' ? 'aspect-square' : 'w-24 h-24 flex-shrink-0 rounded-lg'
                }`}
                onClick={() => setSelectedPhoto(photo)}
              >
                <Image
                  src={photo.image_url}
                  alt={photo.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                {/* Delete button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDeletePhoto(photo.id)
                  }}
                  className="absolute top-2 right-2 p-1.5 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-700"
                  title="Delete photo"
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>

              <div className={`${viewMode === 'grid' ? 'p-4' : 'ml-4 flex-1'}`}>
                <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                  {photo.title}
                </h3>
                {photo.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                    {photo.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                    <Calendar className="h-4 w-4" />
                    <span>{new Date(photo.upload_date).toLocaleDateString()}</span>
                  </div>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleLike(photo.id)
                    }}
                    className={`flex items-center space-x-1 px-2 py-1 rounded-lg transition-colors ${
                      likedPhotos.has(photo.id)
                        ? 'text-red-600 bg-red-50 dark:bg-red-900/20'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700'
                    }`}
                  >
                    <Heart 
                      className={`h-4 w-4 ${likedPhotos.has(photo.id) ? 'fill-current' : ''}`} 
                    />
                    <span className="text-sm">{photo.likes_count}</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Upload Modal */}
      <UploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUpload={handlePhotoUpload}
      />

      {/* Photo Viewer Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <button
            onClick={() => setSelectedPhoto(null)}
            className="absolute top-4 right-4 p-2 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors z-10"
          >
            <X className="h-6 w-6" />
          </button>

          <button
            onClick={() => handleDeletePhoto(selectedPhoto.id)}
            className="absolute top-4 right-16 p-2 rounded-full bg-red-600/80 text-white hover:bg-red-600 transition-colors z-10"
            title="Delete photo"
          >
            <Trash2 className="h-6 w-6" />
          </button>

          <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
            <div className="relative">
              <Image
                src={selectedPhoto.image_url}
                alt={selectedPhoto.title}
                width={800}
                height={800}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              />
            </div>
          </div>

          <div className="absolute bottom-4 left-4 right-4 text-center">
            <div className="bg-black/50 backdrop-blur-sm rounded-lg px-6 py-4 inline-block max-w-2xl">
              <h3 className="text-white font-semibold text-lg mb-2">{selectedPhoto.title}</h3>
              {selectedPhoto.description && (
                <p className="text-gray-200 text-sm">{selectedPhoto.description}</p>
              )}
              <div className="flex items-center justify-center space-x-4 mt-3 text-sm text-gray-300">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(selectedPhoto.upload_date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Heart className="h-4 w-4" />
                  <span>{selectedPhoto.likes_count} likes</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
