'use client'

import { User } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'

interface CrispProfilePictureProps {
  imageUrl?: string | null
  customerName: string
  customerFamilyName: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  forceRefresh?: boolean // Add prop to force cache refresh
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-40 h-40'
}

const iconSizes = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8',
  lg: 'h-12 w-12',
  xl: 'h-16 w-16'
}

export default function CrispProfilePicture({
  imageUrl,
  customerName,
  customerFamilyName,
  size = 'xl',
  className = '',
  forceRefresh = false
}: CrispProfilePictureProps) {
  const [imageError, setImageError] = useState(false)

  // Generate initials from customer name
  const getInitials = () => {
    const firstInitial = customerName.charAt(0).toUpperCase()
    const lastInitial = customerFamilyName.charAt(0).toUpperCase()
    return `${firstInitial}${lastInitial}`
  }

  // Generate a consistent color based on the customer name
  const getAvatarColor = () => {
    const colors = [
      'bg-red-500',
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500',
      'bg-orange-500',
      'bg-cyan-500'
    ]
    
    const nameHash = (customerName + customerFamilyName).split('').reduce((hash, char) => {
      return char.charCodeAt(0) + ((hash << 5) - hash)
    }, 0)
    
    return colors[Math.abs(nameHash) % colors.length]
  }

  const shouldShowImage = imageUrl && !imageError

  // Create cache-busted URL if forceRefresh is true
  const getImageUrl = () => {
    if (!imageUrl) return ''

    if (forceRefresh) {
      const timestamp = Date.now()
      return imageUrl.includes('?')
        ? `${imageUrl}&cache_bust=${timestamp}`
        : `${imageUrl}?cache_bust=${timestamp}`
    }

    return imageUrl
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`${sizeClasses[size]} rounded-full overflow-hidden border-2 border-white shadow-md`}
        style={{
          backgroundColor: shouldShowImage ? 'transparent' : undefined
        }}
      >
        {shouldShowImage ? (
          <Image
            src={getImageUrl()}
            alt={`${customerName} ${customerFamilyName}`}
            fill
            className="object-cover"
            style={{
              imageRendering: 'auto',
              filter: 'none',
              WebkitFilter: 'none'
            }}
            onError={() => setImageError(true)}
            key={forceRefresh ? Date.now() : imageUrl} // Force re-render when forceRefresh changes
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div
            className={`w-full h-full flex items-center justify-center text-white font-semibold ${getAvatarColor()}`}
          >
            {getInitials() ? (
              <span className={`${
                size === 'xl' ? 'text-2xl' : 
                size === 'lg' ? 'text-lg' : 
                size === 'md' ? 'text-sm' : 
                'text-xs'
              }`}>
                {getInitials()}
              </span>
            ) : (
              <User className={iconSizes[size]} />
            )}
          </div>
        )}
      </div>
    </div>
  )
}
