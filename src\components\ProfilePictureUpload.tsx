'use client'

import { Camera, Upload, User, X } from 'lucide-react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useState, useRef } from 'react'

import { logger } from '@/lib/logger'
import { getOptimizedProfilePictureUrl } from '@/utils'

interface ProfilePictureUploadProps {
  currentImageUrl?: string
  currentPublicId?: string // Add current public_id for cleanup
  onImageChange: (imageUrl: string | null, publicId: string | null) => void
  size?: 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  className?: string
  isDebtModal?: boolean // New prop to identify debt modal context
}

const sizeClasses: Record<'sm' | 'md' | 'lg' | 'xl', string> = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-40 h-40'
}

const iconSizes = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8',
  lg: 'h-12 w-12', 
  xl: 'h-16 w-16'
}

export default function ProfilePictureUpload({
  currentImageUrl,
  currentPublicId,
  onImageChange,
  size = 'lg',
  disabled = false,
  className = '',
  isDebtModal = false
}: ProfilePictureUploadProps) {
  const { resolvedTheme } = useTheme()
  const [isUploading, setIsUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [imageKey, setImageKey] = useState(Date.now()) // Add key for forcing re-render
  const fileInputRef = useRef<HTMLInputElement>(null)

  const getSizeValue = (sizeKey: 'sm' | 'md' | 'lg' | 'xl'): string => {
    const sizeClass = sizeClasses[sizeKey]
    return sizeClass.split(' ')[0]?.replace('w-', '') || '64'
  }

  const handleFileSelect = async (file: File) => {
    if (!file || disabled) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB')
      return
    }

    // Check image dimensions for optimal quality
    const img = new window.Image()
    const imageUrl = URL.createObjectURL(file)

    img.onload = () => {
      URL.revokeObjectURL(imageUrl)

      // Recommend minimum dimensions for crisp display
      if (img.width < 400 || img.height < 400) {
        const proceed = confirm(
          `Image resolution is ${img.width}x${img.height}px. For best quality, we recommend at least 400x400px. Continue anyway?`
        )
        if (!proceed) return
      }

      // Proceed with upload
      uploadFile(file)
    }

    img.onerror = () => {
      URL.revokeObjectURL(imageUrl)
      alert('Invalid image file')
    }

    img.src = imageUrl
  }

  const uploadFile = async (file: File) => {
    setIsUploading(true)

    try {
      // Create FormData for upload
      const formData = new FormData()
      formData.append('file', file)

      // Include current public_id for automatic cleanup of old image
      if (currentPublicId) {
        formData.append('old_public_id', currentPublicId)
      }

      // Upload using internal API route
      const response = await fetch('/api/upload/profile-picture', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const data = await response.json()
      setImageKey(Date.now()) // Force re-render with new image
      onImageChange(data.url, data.public_id)
    } catch (error) {
      logger.error('Upload error', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image. Please try again.'
      alert(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0 && files[0]) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleRemove = async (e: React.MouseEvent) => {
    e.stopPropagation()

    // Delete from Cloudinary if there's a public_id
    if (currentPublicId) {
      try {
        const response = await fetch(`/api/upload/profile-picture?public_id=${encodeURIComponent(currentPublicId)}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          logger.info(`Successfully deleted profile picture: ${currentPublicId}`)
        } else {
          logger.error('Failed to delete profile picture from Cloudinary')
        }
      } catch (error) {
        logger.error('Error deleting profile picture', error)
      }
    }

    onImageChange(null, null)
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`
          ${sizeClasses[size]} 
          relative rounded-full overflow-hidden border-2 cursor-pointer transition-all duration-200
          ${dragOver ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'}
          ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:border-blue-400 hover:shadow-lg'}
          ${isUploading ? 'animate-pulse' : ''}
        `}
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb'
        }}
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {currentImageUrl ? (
          <>
            {(() => {
              const optimizedUrl = getOptimizedProfilePictureUrl(currentImageUrl)
              logger.debug('Profile Picture URL', { original: currentImageUrl, optimized: optimizedUrl, isDebtModal })

              // For debt modal, use regular img tag with cache-busting
              if (isDebtModal) {
                const cacheBustUrl = currentImageUrl.includes('?')
                  ? `${currentImageUrl}&cache_bust=${imageKey}`
                  : `${currentImageUrl}?cache_bust=${imageKey}`

                return (
                  <Image
                    src={cacheBustUrl}
                    alt="Profile"
                    fill
                    className="object-cover customer-debt-profile"
                    style={{
                      imageRendering: 'auto',
                      filter: 'none',
                      WebkitFilter: 'none'
                    }}
                    key={imageKey} // Force re-render when image changes
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                )
              }

              return (
                <Image
                  src={optimizedUrl}
                  alt="Profile"
                  fill
                  className="object-cover profile-picture"
                  sizes={`${getSizeValue(size)}px`}
                  quality={100}
                  priority={true}
                  unoptimized={false}
                  style={{
                    imageRendering: 'auto',
                    filter: 'none',
                    WebkitFilter: 'none'
                  }}
                  key={imageKey} // Force re-render when image changes
                />
              )
            })()}
            {!disabled && (
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                <Camera className="h-6 w-6 text-white opacity-0 hover:opacity-100 transition-opacity duration-200" />
              </div>
            )}
            {!disabled && (
              <button
                onClick={handleRemove}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors shadow-lg"
                title="Remove photo"
              >
                <X className="h-3 w-3" />
              </button>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            {isUploading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            ) : (
              <>
                <User className={iconSizes[size]} />
                {size !== 'sm' && (
                  <div className="mt-2 text-center">
                    <Upload className="h-4 w-4 mx-auto mb-1" />
                    <p className="text-xs">Upload Photo</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file) handleFileSelect(file)
        }}
        className="hidden"
        disabled={disabled}
      />

      {size !== 'sm' && (
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
          Click or drag to upload
          <br />
          Max 5MB, JPG/PNG
        </p>
      )}
    </div>
  )
}
