-- =====================================================
-- TINDAHAN STORE - CLEAN DATABASE SCHEMA
-- =====================================================
-- Simplified, error-free schema for immediate deployment
-- This creates the core tables needed for your application

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For fuzzy text search

-- =====================================================
-- SAFE CLEANUP (PREVENTS CONFLICTS)
-- =====================================================
-- Drop existing objects in correct dependency order

-- Drop policies first
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_payments;
DROP POLICY IF EXISTS "Enable all operations for application" ON products;
DROP POLICY IF EXISTS "Enable all operations for application" ON customers;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_payments;

-- Drop views
DROP VIEW IF EXISTS customer_balances CASCADE;

-- Drop triggers
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS customer_payments CASCADE;
DROP TABLE IF EXISTS customer_debts CASCADE;
DROP TABLE IF EXISTS customers CASCADE;
DROP TABLE IF EXISTS products CASCADE;

-- =====================================================
-- CORE TABLES DEFINITION
-- =====================================================

-- PRODUCTS TABLE - Inventory management
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    image_public_id TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    retail_price DECIMAL(10,2) CHECK (retail_price IS NULL OR retail_price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Data integrity constraints
    CONSTRAINT products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT products_category_not_empty CHECK (LENGTH(TRIM(category)) > 0),
    CONSTRAINT products_net_weight_not_empty CHECK (LENGTH(TRIM(net_weight)) > 0)
);

-- CUSTOMERS TABLE - Profile management
CREATE TABLE customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT,
    phone_number VARCHAR(20),
    address TEXT,
    birth_date DATE,
    birth_place VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint and data validation
    UNIQUE(customer_name, customer_family_name),
    CONSTRAINT customers_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customers_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customers_phone_format CHECK (phone_number IS NULL OR phone_number ~ '^[0-9+\-\s()]+$'),
    CONSTRAINT customers_birth_date_valid CHECK (birth_date IS NULL OR birth_date <= CURRENT_DATE),
    CONSTRAINT customers_birth_place_not_empty CHECK (birth_place IS NULL OR LENGTH(TRIM(birth_place)) > 0)
);

-- CUSTOMER DEBTS TABLE - Debt tracking
CREATE TABLE customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price > 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (product_price * quantity) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Business rule constraints
    CONSTRAINT customer_debts_customer_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_debts_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_debts_product_name_not_empty CHECK (LENGTH(TRIM(product_name)) > 0),
    CONSTRAINT customer_debts_reasonable_price CHECK (product_price <= 100000.00),
    CONSTRAINT customer_debts_reasonable_quantity CHECK (quantity <= 1000),
    CONSTRAINT customer_debts_valid_date CHECK (debt_date >= '2020-01-01' AND debt_date <= CURRENT_DATE + INTERVAL '1 day')
);

-- CUSTOMER PAYMENTS TABLE - Payment tracking
CREATE TABLE customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    responsible_family_member VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Business rule constraints
    CONSTRAINT customer_payments_customer_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_payments_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_payments_reasonable_amount CHECK (payment_amount <= 50000.00),
    CONSTRAINT customer_payments_valid_date CHECK (payment_date >= '2020-01-01' AND payment_date <= CURRENT_DATE + INTERVAL '1 day'),
    CONSTRAINT customer_payments_valid_method CHECK (payment_method IN ('Cash', 'GCash', 'PayMaya', 'Bank Transfer', 'Others'))
);

-- =====================================================
-- CUSTOMER BALANCE VIEW
-- =====================================================
CREATE VIEW customer_balances
WITH (security_invoker = true) AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    CASE
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0
        THEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0)
        ELSE 0
    END as remaining_balance,
    CASE
        WHEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0) > 0
        THEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0)
        ELSE 0
    END as change_amount,
    last_debt_date,
    last_payment_date,
    debt_count,
    payment_count,
    CASE
        WHEN COALESCE(total_debt, 0) = 0 THEN 'No Debt'
        WHEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0) > 0 THEN 'Overpaid'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) = 0 THEN 'Paid'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0 AND
             COALESCE(total_payments, 0) > 0 THEN 'Outstanding'
        WHEN COALESCE(total_debt, 0) > 0 AND COALESCE(total_payments, 0) = 0 THEN 'Unpaid'
        ELSE 'Unknown'
    END as balance_status,
    ROUND(
        CASE
            WHEN COALESCE(total_debt, 0) > 0
            THEN LEAST((COALESCE(total_payments, 0) / total_debt) * 100, 100)
            ELSE 0
        END, 2
    ) as payment_percentage
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date,
        COUNT(*) as debt_count
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date,
        COUNT(*) as payment_count
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- =====================================================
-- BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Automatic timestamp update function
CREATE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- =====================================================
-- AUTOMATIC TRIGGERS
-- =====================================================

-- Timestamp update triggers
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Products table indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);

-- Customers table indexes
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name, customer_family_name);

-- Customer debts table indexes
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_date ON customer_debts(debt_date);

-- Customer payments table indexes
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);

-- =====================================================
-- SECURITY POLICIES (ROW LEVEL SECURITY)
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_payments ENABLE ROW LEVEL SECURITY;

-- Application-friendly policies
CREATE POLICY "Enable all operations for application" ON products FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customers FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customer_debts FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customer_payments FOR ALL USING (true);

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '🎉 TINDAHAN CLEAN DATABASE SCHEMA COMPLETE!';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ CORE TABLES SUCCESSFULLY CREATED:';
    RAISE NOTICE '   📦 Products table with inventory management';
    RAISE NOTICE '   👥 Customers table with profile management';
    RAISE NOTICE '   💰 Customer debts table with debt tracking';
    RAISE NOTICE '   💳 Customer payments table with payment tracking';
    RAISE NOTICE '   📊 Customer balances view with real-time calculations';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 READY FOR USE!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '   1. Test with your application';
    RAISE NOTICE '   2. Add your products and customers';
    RAISE NOTICE '   3. Start recording debts and payments';
    RAISE NOTICE '';
END $$;
