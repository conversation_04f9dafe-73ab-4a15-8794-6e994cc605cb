-- =====================================================
-- CHECK CUSTOMER DEBTS TABLE STRUCTURE
-- =====================================================
-- This script checks if the customer_debts table exists
-- and has the correct structure for debt management
-- =====================================================

-- Check if customer_debts table exists
SELECT 
    'Table Existence Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'customer_debts'
        ) THEN 'EXISTS'
        ELSE 'MISSING'
    END as table_status;

-- Check table structure if it exists
SELECT 
    'Column Structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'customer_debts'
ORDER BY ordinal_position;

-- Check constraints
SELECT 
    'Table Constraints' as check_type,
    constraint_name,
    constraint_type
FROM information_schema.table_constraints
WHERE table_schema = 'public' 
AND table_name = 'customer_debts';

-- Test insert to see what error occurs
-- This will help us identify the exact issue
INSERT INTO customer_debts (
    customer_name, 
    customer_family_name, 
    product_name, 
    product_price, 
    quantity, 
    debt_date, 
    notes
) VALUES (
    'Test Customer',
    'Test Family',
    'Test Product',
    25.00,
    1,
    CURRENT_DATE,
    'Test debt record - safe to delete'
);

-- Check if the insert worked
SELECT 
    'Test Insert Result' as check_type,
    COUNT(*) as total_records,
    MAX(created_at) as latest_record
FROM customer_debts
WHERE customer_name = 'Test Customer';

-- Clean up test record
DELETE FROM customer_debts 
WHERE customer_name = 'Test Customer' 
AND customer_family_name = 'Test Family';
