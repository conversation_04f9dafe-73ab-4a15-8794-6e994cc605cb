// Simple event system for profile picture updates
type ProfilePictureUpdateListener = (customerName: string, customerFamilyName: string, newImageUrl: string) => void

class ProfilePictureEventManager {
  private listeners: ProfilePictureUpdateListener[] = []

  // Subscribe to profile picture updates
  subscribe(listener: ProfilePictureUpdateListener) {
    this.listeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // Notify all listeners of a profile picture update
  notifyUpdate(customerName: string, customerFamilyName: string, newImageUrl: string) {
    this.listeners.forEach(listener => {
      try {
        listener(customerName, customerFamilyName, newImageUrl)
      } catch (error) {
        console.error('Error in profile picture update listener:', error)
      }
    })
  }
}

// Global instance
export const profilePictureEvents = new ProfilePictureEventManager()
