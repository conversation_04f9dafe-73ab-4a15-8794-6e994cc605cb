# 🏪 Revantad Store Admin Dashboard - Professional Setup Guide

## 🌟 Welcome to Your Modern Sari-Sari Store Management System

This comprehensive setup guide will help you deploy a professional-grade admin dashboard for your sari-sari store business. Built with cutting-edge technology including Next.js 15, React 19, TypeScript, and AI-powered features.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Environment Configuration](#environment-configuration)
4. [Database Setup](#database-setup)
5. [Cloud Services Setup](#cloud-services-setup)
6. [AI Features Setup](#ai-features-setup)
7. [Development Server](#development-server)
8. [Feature Overview](#feature-overview)
9. [Troubleshooting](#troubleshooting)
10. [Production Deployment](#production-deployment)

## 🔧 Prerequisites

### System Requirements
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **Git**: For version control
- **Modern Browser**: Chrome, Firefox, Safari, or Edge

### Required Cloud Services
You'll need free accounts for:
- **[Supabase](https://supabase.com)** - PostgreSQL database and authentication
- **[Cloudinary](https://cloudinary.com)** - Image storage and optimization
- **[Google AI Studio](https://aistudio.google.com)** - Gemini AI for intelligent assistant (optional)

## 🚀 Quick Start

### 1. Initial Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd tindahan

# Install dependencies
npm install

# Run the automated setup script
npm run setup
```

The setup script will:
- ✅ Check Node.js version compatibility
- ✅ Install all dependencies
- ✅ Create `.env.local` from `.env.example`
- ✅ Validate environment configuration
- ✅ Display next steps

## ⚙️ Environment Configuration

### 1. Environment Variables Setup

The application uses a comprehensive environment validation system. Copy `.env.example` to `.env.local` and configure:

```env
# =============================================================================
# SUPABASE CONFIGURATION (Required)
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# CLOUDINARY CONFIGURATION (Required for image uploads)
# =============================================================================
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# =============================================================================
# AUTHENTICATION CONFIGURATION (Required)
# =============================================================================
NEXTAUTH_SECRET=your_secure_random_string_here
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# AI CONFIGURATION (Optional - for AI Assistant features)
# =============================================================================
GEMINI_API_KEY=your_google_gemini_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION (Optional)
# =============================================================================
NODE_ENV=development
DEBUG=false
```

### 2. Environment Validation

The application includes automatic environment validation:
- **Development**: Warns about missing optional variables
- **Production**: Enforces all required variables
- **Runtime**: Validates configuration on startup

## 🗄️ Database Setup

### 1. Create Supabase Project

1. **Sign up/Login** to [Supabase](https://supabase.com)
2. **Create New Project**:
   - Click "New Project"
   - Choose your organization
   - Enter project name: `revantad-store`
   - Set a secure database password
   - Select region closest to your location
   - Click "Create new project"

3. **Wait for Setup** (2-3 minutes)

### 2. Get Supabase Credentials

1. Go to **Settings > API** in your Supabase dashboard
2. Copy the following values:
   - **Project URL** → `NEXT_PUBLIC_SUPABASE_URL`
   - **anon public key** → `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - **service_role key** → `SUPABASE_SERVICE_ROLE_KEY`

### 3. Execute Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy the entire contents of `database/tindahan_unified_schema.sql` (MASTER SCHEMA)
3. Paste into the SQL Editor
4. Click **"Run"** to execute

This will create:
- ✅ **Products table** with 25+ sample products across 8 categories
- ✅ **Customers table** with Cloudinary support and 8 sample profiles
- ✅ **Customer debts table** with 15+ realistic debt transactions
- ✅ **Customer payments table** with 12+ payment records
- ✅ **Enhanced customer_balances view** with status indicators
- ✅ **20+ performance indexes** including fuzzy search support
- ✅ **Automatic triggers** for timestamp management and validation
- ✅ **Row Level Security** optimized for custom authentication
- ✅ **Comprehensive sample data** for immediate testing

**🎯 IMPORTANT:** Use `tindahan_unified_schema.sql` - this is the master schema that replaces all other database files.

### 4. Verify Database Setup

The unified schema includes automatic verification. After running the schema, you should see:

**✅ Verification Output:**
- Tables: 4 (products, customers, customer_debts, customer_payments)
- Views: 1 (customer_balances)
- Functions: 2 (timestamp and validation functions)
- Indexes: 20+ (performance optimized)
- Policies: 4 (RLS security policies)

**✅ Sample Data Counts:**
- Products: 25+ items
- Customers: 8 profiles
- Debts: 15+ transactions
- Payments: 12+ records
- Balances: Real-time calculations

**Manual Verification (Optional):**
```sql
-- Run this in SQL Editor to verify manually
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('products', 'customers', 'customer_debts', 'customer_payments');
```

You should see all 4 tables listed.

## ☁️ Cloud Services Setup

### 1. Cloudinary Setup (Image Management)

**Create Account:**
1. Go to [Cloudinary](https://cloudinary.com) and sign up
2. Navigate to your **Dashboard**
3. Note these credentials:
   - **Cloud Name** → `NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME`
   - **API Key** → `CLOUDINARY_API_KEY`
   - **API Secret** → `CLOUDINARY_API_SECRET`

**Create Upload Preset:**
1. Go to **Settings > Upload**
2. Click **"Add upload preset"**
3. Configure:
   - **Preset name**: `sari-sari-products`
   - **Signing Mode**: `Unsigned`
   - **Folder**: `revantad-store/products` (optional)
   - **Allowed formats**: `jpg,png,webp,gif`
   - **Max file size**: `10MB`
4. Click **"Save"**

### 2. Authentication Setup

Generate a secure secret for NextAuth:

```bash
# Option 1: Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Option 2: Using OpenSSL
openssl rand -hex 32

# Option 3: Online generator
# Visit: https://generate-secret.vercel.app/32
```

Add the generated secret to `NEXTAUTH_SECRET` in your `.env.local`

## 🤖 AI Features Setup (Optional)

### 1. Google Gemini AI Setup

**Get API Key:**
1. Go to [Google AI Studio](https://aistudio.google.com)
2. Sign in with your Google account
3. Click **"Get API Key"**
4. Create a new API key
5. Copy the key → `GEMINI_API_KEY`

**Features Enabled:**
- 🤖 **AI Assistant**: Floating chat widget
- 💬 **Business Support**: Intelligent recommendations
- 📊 **Data Analysis**: AI-powered insights
- 🗣️ **Voice Commands**: Speech-to-text integration

### 2. Voice Features (Included)

The application includes Google Speech-to-Text integration:
- **Real-time transcription**
- **Multiple language support**
- **Professional business context**
- **Confidence scoring**

## 🚀 Development Server

### 1. Start the Application

```bash
# Start development server with Turbopack
npm run dev

# Alternative: Standard Next.js dev server
npm run dev -- --turbo false
```

### 2. Access the Dashboard

Open your browser and navigate to:
- **Local**: http://localhost:3000
- **Network**: http://[your-ip]:3000

### 3. Default Login Credentials

For development and testing:
- **Email**: `<EMAIL>`
- **Password**: `admin123`

## 🎯 Feature Overview

### 📊 Dashboard & Analytics
- **Real-time Statistics**: Products, debts, revenue tracking
- **Interactive Charts**: Sales trends, debt analysis, performance metrics
- **Business Intelligence**: AI-powered insights and recommendations
- **Responsive Design**: Perfect on desktop, tablet, and mobile

### 📦 Product Management (Full CRUD)
- ✅ **Create**: Add new products with images and details
- ✅ **Read**: View organized product lists with search/filter
- ✅ **Update**: Edit product information and stock levels
- ✅ **Delete**: Remove products with confirmation
- ✅ **Categories**: 13 predefined categories for Filipino sari-sari stores
- ✅ **Stock Alerts**: Automatic low stock notifications (< 10 items)
- ✅ **Image Upload**: Cloudinary integration for optimized images

### 💰 Customer Debt Management (Utang System)
- ✅ **Add Debts**: Record customer purchases on credit
- ✅ **Track Payments**: Monitor outstanding balances
- ✅ **Customer Grouping**: Organize debts by customer family
- ✅ **Search & Filter**: Find debts by customer or product
- ✅ **Date Tracking**: Automatic debt date recording
- ✅ **Calculated Totals**: Automatic amount calculations

### 🤖 AI-Powered Features
- **Floating AI Assistant**: Context-aware business support
- **Voice Commands**: Speech-to-text for hands-free operation
- **Business Insights**: AI analysis of sales and inventory data
- **Smart Recommendations**: Product restocking and pricing suggestions
- **Multi-language Support**: English and Filipino context understanding

### 📱 Additional Features
- **Calendar System**: Track important dates and events
- **History Tracking**: Comprehensive activity logs
- **Settings Panel**: Customize store preferences and branding
- **Dark/Light Mode**: Professional theme switching
- **Responsive Design**: Mobile-first approach

### 🔒 Security & Performance
- **TypeScript**: Full type safety and better development experience
- **Environment Validation**: Automatic configuration checking
- **Error Handling**: Comprehensive error management
- **Performance Optimization**: Next.js 15 with Turbopack
- **Image Optimization**: Automatic image compression and resizing

## 🔧 Troubleshooting

### Common Issues & Solutions

#### 1. Environment Variable Issues
**Problem**: "Missing required environment variables"
**Solution**:
```bash
# Check if .env.local exists
ls -la .env.local

# Validate environment variables
npm run type-check

# Copy from example if missing
cp .env.example .env.local
```

#### 2. Database Connection Issues
**Problem**: "Failed to fetch" or database errors
**Solutions**:
- ✅ Verify Supabase URL format: `https://[project-id].supabase.co`
- ✅ Check API keys are copied correctly (no extra spaces)
- ✅ Ensure database schema has been executed
- ✅ Verify project is not paused in Supabase dashboard

#### 3. Image Upload Issues
**Problem**: Image uploads fail or don't display
**Solutions**:
- ✅ Verify Cloudinary credentials are correct
- ✅ Check upload preset `sari-sari-products` exists
- ✅ Ensure preset is set to "Unsigned"
- ✅ Check file size limits (max 10MB)

#### 4. AI Features Not Working
**Problem**: AI assistant doesn't respond
**Solutions**:
- ✅ Verify `GEMINI_API_KEY` is set correctly
- ✅ Check API key has proper permissions
- ✅ Ensure you haven't exceeded API limits
- ✅ Check browser console for error messages

#### 5. Development Server Issues
**Problem**: Server won't start or crashes
**Solutions**:
```bash
# Clear Next.js cache
npm run clean

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 18.0.0+

# Start with verbose logging
npm run dev -- --verbose
```

### Debug Mode

Enable debug mode for detailed logging:
```env
# Add to .env.local
DEBUG=true
```

This will show:
- Environment variable validation
- API request/response details
- Database query information
- Performance metrics

## 🚀 Production Deployment

### 1. Pre-deployment Checklist

- ✅ All environment variables configured
- ✅ Database schema executed successfully
- ✅ Cloudinary upload preset created
- ✅ Application tested locally
- ✅ No TypeScript errors: `npm run type-check`
- ✅ No ESLint errors: `npm run lint`

### 2. Environment Variables for Production

Update these variables for production:

```env
# Production Configuration
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
DEBUG=false

# Keep all other variables the same
# Supabase, Cloudinary, and Gemini keys work in production
```

### 3. Deployment Platforms

#### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
# Project Settings > Environment Variables
```

#### Netlify
```bash
# Install Netlify CLI
npm i -g netlify-cli

# Build and deploy
npm run build
netlify deploy --prod --dir=.next
```

#### Docker Deployment
```dockerfile
# Dockerfile included in project
docker build -t revantad-store .
docker run -p 3000:3000 revantad-store
```

### 4. Post-deployment Testing

Test all features in production:
- ✅ Dashboard loads and displays data
- ✅ Product CRUD operations work
- ✅ Image uploads to Cloudinary function
- ✅ Customer debt management works
- ✅ AI assistant responds correctly
- ✅ Mobile responsiveness
- ✅ Performance metrics acceptable

## 📚 Additional Resources

### Documentation
- **Setup Guide**: `docs/SETUP_GUIDE.md` (this file)
- **Deployment Guide**: `docs/DEPLOYMENT.md`
- **Voice Integration**: `docs/VOICE_RECORDING_GOOGLE_INTEGRATION.md`
- **Main README**: `README.md`

### Available Scripts
```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Quality Assurance
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run type-check   # TypeScript validation

# Utilities
npm run clean        # Clear build cache
npm run setup        # Run setup script
npm run preview      # Build and preview
```

### Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4, Framer Motion
- **Database**: Supabase (PostgreSQL)
- **Storage**: Cloudinary
- **AI**: Google Gemini AI
- **Charts**: ECharts
- **Icons**: Lucide React
- **Forms**: React Hook Form + Zod validation

### Support & Community
- **GitHub Issues**: Report bugs and request features
- **Documentation**: Comprehensive guides and examples
- **Code Quality**: ESLint, TypeScript, and automated testing
- **Performance**: Optimized for speed and efficiency

## 🎉 Congratulations!

Your **Revantad Store Admin Dashboard** is now fully configured and ready to help you manage your sari-sari store business efficiently!

### What You've Accomplished:
✅ **Professional Dashboard**: Modern, responsive admin interface
✅ **Complete CRUD Operations**: Products and customer debt management
✅ **AI-Powered Features**: Intelligent business assistant and insights
✅ **Cloud Integration**: Scalable database and image storage
✅ **Mobile-Ready**: Perfect experience on all devices
✅ **Production-Ready**: Secure, optimized, and deployable

### Next Steps:
1. **Customize**: Update store branding and preferences in Settings
2. **Add Data**: Start adding your actual products and customer information
3. **Explore Features**: Try the AI assistant, voice commands, and analytics
4. **Deploy**: When ready, deploy to production using the deployment guide
5. **Scale**: The system is designed to grow with your business

**Happy selling! 🏪✨**

---

*For technical support or questions, refer to the documentation or create an issue in the project repository.*
