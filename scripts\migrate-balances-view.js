const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration() {
  try {
    console.log('🚀 Starting database migration...')
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../database/add_profile_pictures_to_balances_view.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')
    
    // Split SQL commands (simple split by semicolon)
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'))
    
    console.log(`📝 Found ${commands.length} SQL commands to execute`)
    
    // Execute each command
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i]
      if (command.toLowerCase().includes('select')) {
        // For SELECT statements, use the query method
        console.log(`🔍 Executing query ${i + 1}/${commands.length}...`)
        const { data, error } = await supabase.rpc('exec_sql', { sql: command })
        if (error) {
          console.error(`❌ Error in command ${i + 1}:`, error)
        } else {
          console.log(`✅ Query ${i + 1} completed successfully`)
          if (data) console.log('Result:', data)
        }
      } else {
        // For DDL statements, use rpc to execute raw SQL
        console.log(`⚙️  Executing command ${i + 1}/${commands.length}...`)
        const { error } = await supabase.rpc('exec_sql', { sql: command })
        if (error) {
          console.error(`❌ Error in command ${i + 1}:`, error)
        } else {
          console.log(`✅ Command ${i + 1} completed successfully`)
        }
      }
    }
    
    console.log('🎉 Migration completed successfully!')
    
  } catch (error) {
    console.error('💥 Migration failed:', error)
    process.exit(1)
  }
}

runMigration()
