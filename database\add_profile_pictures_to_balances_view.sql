-- =====================================================
-- ADD PROFILE PICTURES TO CUSTOMER BALANCES VIEW
-- =====================================================
-- This migration creates a new view that includes customer profile pictures
-- in the customer balances data for the debt management section.

-- Create the enhanced customer_balances view with profile pictures
CREATE OR REPLACE VIEW customer_balances_with_profiles
WITH (security_invoker = true) AS
SELECT
    cb.*,
    c.profile_picture_url,
    c.profile_picture_public_id
FROM customer_balances cb
LEFT JOIN customers c ON (
    cb.customer_name = c.customer_name 
    AND cb.customer_family_name = c.customer_family_name
);

-- Grant necessary permissions
GRANT SELECT ON customer_balances_with_profiles TO authenticated;
GRANT SELECT ON customer_balances_with_profiles TO anon;

-- Verify the view was created successfully
SELECT 
    'Customer Balances with Profiles View Created Successfully!' as status,
    COUNT(*) as total_customer_records
FROM customer_balances_with_profiles;

-- Show sample data if any exists
SELECT 
    'Sample Customer Balances with Profiles:' as info,
    customer_name,
    customer_family_name,
    total_debt,
    total_payments,
    remaining_balance,
    balance_status,
    CASE 
        WHEN profile_picture_url IS NOT NULL THEN 'Has Profile Picture'
        ELSE 'No Profile Picture'
    END as profile_status
FROM customer_balances_with_profiles
LIMIT 5;
