import { NextRequest, NextResponse } from 'next/server'
import { logApi } from '@/lib/logger'

interface EnvironmentVariable {
  key: string
  required: boolean
  description: string
  category: 'database' | 'cloudinary' | 'auth' | 'ai' | 'app'
}

const ENVIRONMENT_VARIABLES: EnvironmentVariable[] = [
  // Database
  {
    key: 'NEXT_PUBLIC_SUPABASE_URL',
    required: true,
    description: 'Supabase project URL',
    category: 'database'
  },
  {
    key: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    required: true,
    description: 'Supabase anonymous/public key',
    category: 'database'
  },
  {
    key: 'SUPABASE_SERVICE_ROLE_KEY',
    required: false,
    description: 'Supabase service role key (for admin operations)',
    category: 'database'
  },
  
  // Cloudinary
  {
    key: 'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
    required: true,
    description: 'Cloudinary cloud name',
    category: 'cloudinary'
  },
  {
    key: 'CLOUDINARY_API_KEY',
    required: true,
    description: 'Cloudinary API key',
    category: 'cloudinary'
  },
  {
    key: 'CLOUDINARY_API_SECRET',
    required: true,
    description: 'Cloudinary API secret',
    category: 'cloudinary'
  },
  
  // Authentication
  {
    key: 'NEXTAUTH_SECRET',
    required: false,
    description: 'NextAuth secret for session encryption',
    category: 'auth'
  },
  {
    key: 'NEXTAUTH_URL',
    required: false,
    description: 'NextAuth URL for authentication callbacks',
    category: 'auth'
  },
  
  // AI
  {
    key: 'GEMINI_API_KEY',
    required: false,
    description: 'Google Gemini AI API key',
    category: 'ai'
  },
  
  // Application
  {
    key: 'NODE_ENV',
    required: true,
    description: 'Node.js environment (development/production)',
    category: 'app'
  },
  {
    key: 'DEBUG',
    required: false,
    description: 'Enable debug logging',
    category: 'app'
  }
]

export async function GET(request: NextRequest) {
  try {
    logApi('Checking environment variables', { endpoint: '/api/settings/environment' })

    const results = {
      isValid: true,
      missingRequired: [] as string[],
      missingOptional: [] as string[],
      configured: [] as string[],
      categories: {
        database: { configured: 0, total: 0, missing: [] as string[] },
        cloudinary: { configured: 0, total: 0, missing: [] as string[] },
        auth: { configured: 0, total: 0, missing: [] as string[] },
        ai: { configured: 0, total: 0, missing: [] as string[] },
        app: { configured: 0, total: 0, missing: [] as string[] }
      }
    }

    // Check each environment variable
    ENVIRONMENT_VARIABLES.forEach(envVar => {
      const value = process.env[envVar.key]
      const category = results.categories[envVar.category]
      
      category.total++
      
      if (value && value.trim() !== '') {
        results.configured.push(envVar.key)
        category.configured++
      } else {
        if (envVar.required) {
          results.missingRequired.push(envVar.key)
          results.isValid = false
        } else {
          results.missingOptional.push(envVar.key)
        }
        category.missing.push(envVar.key)
      }
    })

    // Calculate overall health score
    const totalConfigured = results.configured.length
    const totalVariables = ENVIRONMENT_VARIABLES.length
    const healthScore = Math.round((totalConfigured / totalVariables) * 100)

    // Generate recommendations
    const recommendations = []
    
    if (results.missingRequired.length > 0) {
      recommendations.push({
        type: 'critical',
        message: `${results.missingRequired.length} required environment variables are missing`,
        action: 'Configure these variables immediately to ensure proper functionality',
        variables: results.missingRequired
      })
    }

    if (results.categories.database.missing.length > 0) {
      recommendations.push({
        type: 'warning',
        message: 'Database configuration incomplete',
        action: 'Set up Supabase credentials for database functionality',
        variables: results.categories.database.missing
      })
    }

    if (results.categories.cloudinary.missing.length > 0) {
      recommendations.push({
        type: 'warning',
        message: 'Cloudinary configuration incomplete',
        action: 'Configure Cloudinary for image upload functionality',
        variables: results.categories.cloudinary.missing
      })
    }

    if (results.missingOptional.length > 0) {
      recommendations.push({
        type: 'info',
        message: `${results.missingOptional.length} optional features are not configured`,
        action: 'Consider configuring these for enhanced functionality',
        variables: results.missingOptional
      })
    }

    return NextResponse.json({
      success: true,
      isValid: results.isValid,
      healthScore,
      summary: {
        total: totalVariables,
        configured: totalConfigured,
        missingRequired: results.missingRequired.length,
        missingOptional: results.missingOptional.length
      },
      categories: results.categories,
      missingVars: results.missingRequired,
      recommendations,
      details: ENVIRONMENT_VARIABLES.map(envVar => ({
        key: envVar.key,
        configured: !!process.env[envVar.key],
        required: envVar.required,
        description: envVar.description,
        category: envVar.category
      }))
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      message: 'Failed to check environment variables',
      error: error.message
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    logApi('Environment action requested', { endpoint: '/api/settings/environment', action })

    switch (action) {
      case 'validate':
        // Re-run validation (same as GET)
        return GET(request)
        
      case 'generate-template':
        // Generate .env template
        const template = ENVIRONMENT_VARIABLES
          .map(envVar => {
            const comment = `# ${envVar.description} (${envVar.required ? 'Required' : 'Optional'})`
            const placeholder = envVar.required ? 'your_value_here' : ''
            return `${comment}\n${envVar.key}=${placeholder}`
          })
          .join('\n\n')

        return NextResponse.json({
          success: true,
          template,
          message: 'Environment template generated successfully'
        })
        
      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid action specified'
        }, { status: 400 })
    }

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      message: 'Failed to process environment action',
      error: error.message
    }, { status: 500 })
  }
}
