'use client'

import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import {
  Database,
  Cloud,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Eye,
  EyeOff,
  Server,
  Settings as SettingsIcon,
  Wifi,
  HardDrive,
  Activity
} from 'lucide-react'

interface ConnectionStatus {
  status: 'connected' | 'disconnected' | 'testing' | 'error'
  message: string
  lastChecked?: string
}

interface SystemHealth {
  database: ConnectionStatus
  cloudinary: ConnectionStatus
  environment: {
    isValid: boolean
    missingVars: string[]
  }
}

function Settings() {
  const { resolvedTheme } = useTheme()
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    database: { status: 'testing', message: 'Checking connection...' },
    cloudinary: { status: 'testing', message: 'Checking connection...' },
    environment: { isValid: true, missingVars: [] }
  })
  const [showSecrets, setShowSecrets] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)

  const checkSystemHealth = async () => {
    setIsRefreshing(true)
    
    try {
      // Check database connection
      const dbResponse = await fetch('/api/test-connection')
      const dbData = await dbResponse.json()
      
      // Check Cloudinary connection
      const cloudinaryResponse = await fetch('/api/settings/test-cloudinary')
      const cloudinaryData = await cloudinaryResponse.json()
      
      // Check environment variables
      const envResponse = await fetch('/api/settings/environment')
      const envData = await envResponse.json()

      setSystemHealth({
        database: {
          status: dbData.success ? 'connected' : 'error',
          message: dbData.message || (dbData.success ? 'Database connected successfully' : 'Database connection failed'),
          lastChecked: new Date().toLocaleString()
        },
        cloudinary: {
          status: cloudinaryData.success ? 'connected' : 'error',
          message: cloudinaryData.message || (cloudinaryData.success ? 'Cloudinary connected successfully' : 'Cloudinary connection failed'),
          lastChecked: new Date().toLocaleString()
        },
        environment: {
          isValid: envData.isValid || false,
          missingVars: envData.missingVars || []
        }
      })
    } catch (error) {
      console.error('Error checking system health:', error)
      setSystemHealth(prev => ({
        ...prev,
        database: { status: 'error', message: 'Failed to check database connection' },
        cloudinary: { status: 'error', message: 'Failed to check Cloudinary connection' }
      }))
    } finally {
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    checkSystemHealth()
  }, [])

  const getStatusIcon = (status: ConnectionStatus['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'testing':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
    }
  }

  const getStatusColor = (status: ConnectionStatus['status']) => {
    switch (status) {
      case 'connected':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
      case 'testing':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
      default:
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div
            className="p-3 rounded-xl"
            style={{
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'
                : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)',
              border: resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.3)' : '1px solid rgba(34, 197, 94, 0.2)'
            }}
          >
            <SettingsIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">System Settings</h2>
            <p className="text-gray-600 dark:text-gray-400">Manage database and Cloudinary connections</p>
          </div>
        </div>
        <button
          onClick={checkSystemHealth}
          disabled={isRefreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg transition-colors duration-200"
        >
          <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span>Refresh Status</span>
        </button>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Database Status */}
        <div className={`p-6 rounded-xl border-2 transition-all duration-200 ${getStatusColor(systemHealth.database.status)}`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              <h3 className="font-semibold text-gray-900 dark:text-white">Database</h3>
            </div>
            {getStatusIcon(systemHealth.database.status)}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{systemHealth.database.message}</p>
          {systemHealth.database.lastChecked && (
            <p className="text-xs text-gray-500 dark:text-gray-500">
              Last checked: {systemHealth.database.lastChecked}
            </p>
          )}
        </div>

        {/* Cloudinary Status */}
        <div className={`p-6 rounded-xl border-2 transition-all duration-200 ${getStatusColor(systemHealth.cloudinary.status)}`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Cloud className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              <h3 className="font-semibold text-gray-900 dark:text-white">Cloudinary</h3>
            </div>
            {getStatusIcon(systemHealth.cloudinary.status)}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{systemHealth.cloudinary.message}</p>
          {systemHealth.cloudinary.lastChecked && (
            <p className="text-xs text-gray-500 dark:text-gray-500">
              Last checked: {systemHealth.cloudinary.lastChecked}
            </p>
          )}
        </div>

        {/* Environment Status */}
        <div className={`p-6 rounded-xl border-2 transition-all duration-200 ${
          systemHealth.environment.isValid 
            ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
            : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Server className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              <h3 className="font-semibold text-gray-900 dark:text-white">Environment</h3>
            </div>
            {systemHealth.environment.isValid ? (
              <CheckCircle className="w-5 h-5 text-green-500" />
            ) : (
              <XCircle className="w-5 h-5 text-red-500" />
            )}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {systemHealth.environment.isValid 
              ? 'All environment variables configured' 
              : `${systemHealth.environment.missingVars.length} variables missing`
            }
          </p>
          {!systemHealth.environment.isValid && systemHealth.environment.missingVars.length > 0 && (
            <div className="text-xs text-red-600 dark:text-red-400">
              Missing: {systemHealth.environment.missingVars.join(', ')}
            </div>
          )}
        </div>
      </div>

      {/* Detailed Configuration Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Database Configuration */}
        <div className="bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Database Configuration</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Supabase URL
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type={showSecrets ? 'text' : 'password'}
                  value={process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not configured'}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
                />
                <button
                  onClick={() => setShowSecrets(!showSecrets)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  {showSecrets ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Connection Status
              </label>
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemHealth.database.status)}
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {systemHealth.database.message}
                </span>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200 dark:border-slate-600">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => window.open('https://supabase.com/dashboard', '_blank')}
                  className="px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-lg transition-colors"
                >
                  <Wifi className="w-4 h-4 inline mr-1" />
                  Open Dashboard
                </button>
                <button
                  onClick={checkSystemHealth}
                  className="px-3 py-2 text-sm bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:hover:bg-green-900/50 text-green-700 dark:text-green-300 rounded-lg transition-colors"
                >
                  <Activity className="w-4 h-4 inline mr-1" />
                  Test Connection
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Cloudinary Configuration */}
        <div className="bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <Cloud className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Cloudinary Configuration</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cloud Name
              </label>
              <input
                type="text"
                value={process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'Not configured'}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                API Key
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type={showSecrets ? 'text' : 'password'}
                  value={process.env.CLOUDINARY_API_KEY || 'Not configured'}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Connection Status
              </label>
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemHealth.cloudinary.status)}
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {systemHealth.cloudinary.message}
                </span>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200 dark:border-slate-600">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => window.open('https://cloudinary.com/console', '_blank')}
                  className="px-3 py-2 text-sm bg-purple-100 hover:bg-purple-200 dark:bg-purple-900/30 dark:hover:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-lg transition-colors"
                >
                  <HardDrive className="w-4 h-4 inline mr-1" />
                  Open Console
                </button>
                <button
                  onClick={checkSystemHealth}
                  className="px-3 py-2 text-sm bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:hover:bg-green-900/50 text-green-700 dark:text-green-300 rounded-lg transition-colors"
                >
                  <Activity className="w-4 h-4 inline mr-1" />
                  Test Upload
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Environment Variables Section */}
      <div className="bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Server className="w-6 h-6 text-orange-600 dark:text-orange-400" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Environment Variables</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            { key: 'NODE_ENV', value: process.env.NODE_ENV, required: true },
            { key: 'NEXT_PUBLIC_SUPABASE_URL', value: process.env.NEXT_PUBLIC_SUPABASE_URL, required: true },
            { key: 'NEXT_PUBLIC_SUPABASE_ANON_KEY', value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY, required: true },
            { key: 'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME', value: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME, required: true },
            { key: 'CLOUDINARY_API_KEY', value: process.env.CLOUDINARY_API_KEY, required: true },
            { key: 'GEMINI_API_KEY', value: process.env.GEMINI_API_KEY, required: false }
          ].map((env) => (
            <div key={env.key} className="p-3 border border-gray-200 dark:border-slate-600 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{env.key}</span>
                {env.value ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {env.required ? 'Required' : 'Optional'} • {env.value ? 'Configured' : 'Missing'}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Settings
