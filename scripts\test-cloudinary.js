#!/usr/bin/env node

/**
 * Cloudinary Configuration Test Script
 * 
 * This script tests your Cloudinary configuration to ensure
 * image uploads will work properly in your application.
 */

const { v2: cloudinary } = require('cloudinary');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

console.log('🔍 Testing Cloudinary Configuration...\n');

// Check environment variables
const requiredEnvVars = {
  'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME': process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  'CLOUDINARY_API_KEY': process.env.CLOUDINARY_API_KEY,
  'CLOUDINARY_API_SECRET': process.env.CLOUDINARY_API_SECRET
};

console.log('📋 Environment Variables Check:');
let configValid = true;

Object.entries(requiredEnvVars).forEach(([key, value]) => {
  const status = value ? '✅' : '❌';
  const displayValue = value ? (key.includes('SECRET') ? '***hidden***' : value) : 'NOT SET';
  console.log(`   ${status} ${key}: ${displayValue}`);
  
  if (!value) {
    configValid = false;
  }
});

if (!configValid) {
  console.log('\n❌ Missing required environment variables!');
  console.log('\n📝 Please ensure you have set the following in your .env.local file:');
  console.log('   NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name');
  console.log('   CLOUDINARY_API_KEY=your_api_key');
  console.log('   CLOUDINARY_API_SECRET=your_api_secret');
  console.log('\n🔗 Get these from: https://cloudinary.com/console');
  process.exit(1);
}

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

console.log('\n🔧 Cloudinary Configuration:');
console.log(`   Cloud Name: ${cloudinary.config().cloud_name}`);
console.log(`   API Key: ${cloudinary.config().api_key}`);
console.log(`   API Secret: ${cloudinary.config().api_secret ? '***set***' : 'NOT SET'}`);

// Test API connection
async function testCloudinaryConnection() {
  try {
    console.log('\n🌐 Testing Cloudinary API connection...');
    
    // Test with a simple API call
    const result = await cloudinary.api.ping();
    console.log('✅ Cloudinary API connection successful!');
    console.log(`   Status: ${result.status}`);
    
    return true;
  } catch (error) {
    console.log('❌ Cloudinary API connection failed!');
    console.log(`   Error: ${error.message}`);
    
    if (error.message.includes('Invalid API key')) {
      console.log('   💡 Check your CLOUDINARY_API_KEY');
    } else if (error.message.includes('Invalid cloud name')) {
      console.log('   💡 Check your NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME');
    } else if (error.message.includes('Invalid API secret')) {
      console.log('   💡 Check your CLOUDINARY_API_SECRET');
    }
    
    return false;
  }
}

// Test folder access
async function testFolderAccess() {
  try {
    console.log('\n📁 Testing folder access...');
    
    // Try to list resources in the sari-sari-products folder
    const result = await cloudinary.api.resources({
      type: 'upload',
      prefix: 'sari-sari-products/',
      max_results: 1
    });
    
    console.log('✅ Folder access successful!');
    console.log(`   Found ${result.resources.length} existing images in sari-sari-products folder`);
    
    return true;
  } catch (error) {
    console.log('⚠️  Folder access test failed (this is normal for new accounts)');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('\n🚀 Starting Cloudinary tests...\n');
  
  const connectionTest = await testCloudinaryConnection();
  if (!connectionTest) {
    console.log('\n❌ Cannot proceed with further tests due to connection failure.');
    process.exit(1);
  }
  
  await testFolderAccess();
  
  console.log('\n✅ Cloudinary configuration test completed!');
  console.log('\n📝 Next steps:');
  console.log('   1. Try uploading an image through your application');
  console.log('   2. Check the Cloudinary console for uploaded images');
  console.log('   3. If issues persist, check the browser console for detailed errors');
  
  console.log('\n🔗 Useful links:');
  console.log(`   Cloudinary Console: https://cloudinary.com/console`);
  console.log(`   Your Media Library: https://cloudinary.com/console/media_library`);
}

// Run the tests
runTests().catch(error => {
  console.error('\n💥 Test script failed:', error);
  process.exit(1);
});
