import { NextRequest, NextResponse } from 'next/server'
import { v2 as cloudinary } from 'cloudinary'
import { logError, logApi } from '@/lib/logger'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
})

export async function GET(request: NextRequest) {
  try {
    logApi('Testing Cloudinary connection', { endpoint: '/api/settings/test-cloudinary' })

    // Check if required environment variables are present
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME) {
      return NextResponse.json({
        success: false,
        message: 'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME environment variable is missing',
        details: 'Please configure your Cloudinary cloud name in environment variables'
      }, { status: 400 })
    }

    if (!process.env.CLOUDINARY_API_KEY) {
      return NextResponse.json({
        success: false,
        message: 'CLOUDINARY_API_KEY environment variable is missing',
        details: 'Please configure your Cloudinary API key in environment variables'
      }, { status: 400 })
    }

    if (!process.env.CLOUDINARY_API_SECRET) {
      return NextResponse.json({
        success: false,
        message: 'CLOUDINARY_API_SECRET environment variable is missing',
        details: 'Please configure your Cloudinary API secret in environment variables'
      }, { status: 400 })
    }

    // Test Cloudinary connection by getting account details
    const result = await cloudinary.api.ping()
    
    if (result.status === 'ok') {
      // Get additional account information
      const usage = await cloudinary.api.usage()
      
      return NextResponse.json({
        success: true,
        message: 'Cloudinary connection successful',
        details: {
          status: result.status,
          cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
          usage: {
            credits: usage.credits,
            used_percent: usage.used_percent,
            limit: usage.limit
          }
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Cloudinary connection failed',
        details: 'Ping test returned unexpected status'
      }, { status: 500 })
    }

  } catch (error: any) {
    logError('Cloudinary connection test failed', error)
    
    // Handle specific Cloudinary errors
    if (error.http_code === 401) {
      return NextResponse.json({
        success: false,
        message: 'Cloudinary authentication failed',
        details: 'Please check your API key and secret'
      }, { status: 401 })
    }

    if (error.http_code === 404) {
      return NextResponse.json({
        success: false,
        message: 'Cloudinary cloud name not found',
        details: 'Please check your cloud name configuration'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: false,
      message: 'Cloudinary connection test failed',
      details: error.message || 'Unknown error occurred'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    logApi('Testing Cloudinary upload functionality', { endpoint: '/api/settings/test-cloudinary' })

    // Check if required environment variables are present
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 
        !process.env.CLOUDINARY_API_KEY || 
        !process.env.CLOUDINARY_API_SECRET) {
      return NextResponse.json({
        success: false,
        message: 'Cloudinary environment variables are missing',
        details: 'Please configure all required Cloudinary environment variables'
      }, { status: 400 })
    }

    // Create a small test image (1x1 pixel transparent PNG)
    const testImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    )

    // Test upload functionality
    const uploadResult = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: `test_upload_${Date.now()}`,
          folder: 'system_tests',
          transformation: [{ width: 1, height: 1 }]
        },
        (error, result) => {
          if (error) {
            reject(error)
          } else {
            resolve(result)
          }
        }
      ).end(testImageBuffer)
    })

    // Clean up test image
    if (uploadResult && (uploadResult as any).public_id) {
      await cloudinary.uploader.destroy((uploadResult as any).public_id)
    }

    return NextResponse.json({
      success: true,
      message: 'Cloudinary upload test successful',
      details: {
        uploadWorking: true,
        deleteWorking: true,
        cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
      }
    })

  } catch (error: any) {
    logError('Cloudinary upload test failed', error)
    
    return NextResponse.json({
      success: false,
      message: 'Cloudinary upload test failed',
      details: error.message || 'Unknown error occurred during upload test'
    }, { status: 500 })
  }
}
