import { NextRequest } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { logApi, logDb, logError } from '@/lib/logger'
import { supabase } from '@/lib/supabase'

// Types for audit log responses
interface AuditLogResponse {
  logs: Array<{
    id: string
    table_name: string
    operation: string
    record_id: string
    old_values?: Record<string, unknown> | null
    new_values?: Record<string, unknown> | null
    changed_by?: string | null
    changed_at: string
  }>
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch audit logs with optional filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  logApi('GET', '/api/audit-logs')

  const { searchParams } = new URL(request.url)

  // Optional filters
  const table_name = searchParams.get('table')
  const operation = searchParams.get('operation')
  const search = searchParams.get('search')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')

  // Optional pagination - if not specified, return recent logs
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100 // Default to 100 recent logs

  logApi('GET', '/api/audit-logs', undefined, undefined)

  try {
    let query = supabase
      .from('audit_log')
      .select('*', { count: 'exact' })
      .order('changed_at', { ascending: false })

    // Apply filters
    if (table_name) {
      query = query.eq('table_name', table_name)
    }

    if (operation) {
      query = query.eq('operation', operation)
    }

    if (search) {
      query = query.or(`changed_by.ilike.%${search}%,new_values->>name.ilike.%${search}%`)
    }

    if (dateFrom) {
      query = query.gte('changed_at', dateFrom)
    }

    if (dateTo) {
      query = query.lte('changed_at', dateTo)
    }

    // Apply pagination
    if (page && limit) {
      const offset = (page - 1) * limit
      query = query.range(offset, offset + limit - 1)
      logDb('SELECT', 'audit_log', undefined, undefined)
    } else if (limit) {
      query = query.limit(limit)
      logDb('SELECT', 'audit_log', undefined, undefined)
    }

    logDb('SELECT', 'audit_log', undefined, undefined)

    const { data: logs, error, count } = await query

    if (error) {
      logError('Audit log query error', error, { component: 'AuditLogsAPI' })
      throw handleDatabaseError(error)
    }

    const queryTime = Date.now() - startTime
    logDb('SELECT', 'audit_log', queryTime, logs?.length || 0)

    // Return response with or without pagination
    const responseData: AuditLogResponse = {
      logs: logs || [],
    }

    if (page && limit) {
      responseData.pagination = {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    } else {
      responseData.total = count || 0
    }

    return successResponse(responseData)
  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error('❌ Audit log query failed:', error, `after ${queryTime}ms`)
    throw error
  }
})

// POST - Create new audit log entry (for manual logging)
export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json()

  // Validate required fields
  if (!body.table_name || !body.operation || !body.record_id) {
    return successResponse({ error: 'Missing required fields: table_name, operation, record_id' }, undefined, 400)
  }

  const {
    table_name,
    operation,
    record_id,
    old_values,
    new_values,
    changed_by
  } = body

  // Validate operation
  if (!['INSERT', 'UPDATE', 'DELETE'].includes(operation)) {
    return successResponse({ error: 'Invalid operation. Must be INSERT, UPDATE, or DELETE' }, undefined, 400)
  }

  const { data: log, error } = await supabase
    .from('audit_log')
    .insert([{
      table_name,
      operation,
      record_id,
      old_values: old_values || null,
      new_values: new_values || null,
      changed_by: changed_by || 'System'
    }])
    .select()
    .single()

  if (error) {
    console.error('❌ Audit log creation error:', error)
    throw handleDatabaseError(error)
  }

  return successResponse(log, 'Audit log entry created successfully', 201)
})
