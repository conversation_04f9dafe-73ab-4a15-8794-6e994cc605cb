import { NextRequest, NextResponse } from 'next/server'

import cloudinary from '@/lib/cloudinary'
import { logError, logApi } from '@/lib/logger'
import { supabaseAdmin as supabase } from '@/lib/supabase'

// GET - Fetch single product
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { data: product, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    return NextResponse.json({ product })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    )
  }
}

// PUT - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { name, image_url, image_public_id, net_weight, price, retail_price, stock_quantity, category, old_image_public_id } = body

    // Validate required fields
    if (!name || !net_weight || !price || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // If there's a new image and an old image public_id, delete the old image from Cloudinary
    if (image_public_id && old_image_public_id && image_public_id !== old_image_public_id) {
      try {
        logApi('DELETE', `/cloudinary/${old_image_public_id}`, undefined, undefined)
        const deleteResult = await cloudinary.uploader.destroy(old_image_public_id)

        if (deleteResult.result === 'ok') {
          logApi('DELETE', `/cloudinary/${old_image_public_id}`, 200, undefined)
        } else if (deleteResult.result === 'not found') {
          logApi('DELETE', `/cloudinary/${old_image_public_id}`, 404, undefined)
        } else {
          logError(`Failed to delete old product image: ${old_image_public_id}`, deleteResult)
        }
      } catch (deleteError) {
        // Don't fail the update if deletion fails - just log the error
        logError(`Error deleting old product image ${old_image_public_id}`, deleteError)
      }
    }

    const { data: product, error } = await supabase
      .from('products')
      .update({
        name,
        image_url,
        image_public_id,
        net_weight,
        price: parseFloat(price),
        retail_price: retail_price ? parseFloat(retail_price) : null,
        stock_quantity: parseInt(stock_quantity) || 0,
        category,
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ product })
  } catch (error) {
    logError('Product update error', error)
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    )
  }
}

// DELETE - Delete product
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // First, get the product to check if it has an image to delete
    const { data: product, error: fetchError } = await supabase
      .from('products')
      .select('image_public_id')
      .eq('id', id)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = not found
      return NextResponse.json({ error: fetchError.message }, { status: 500 })
    }

    // Delete the product from database
    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 500 })
    }

    // Delete image from Cloudinary if it exists
    if (product?.image_public_id) {
      try {
        logApi('DELETE', `/cloudinary/${product.image_public_id}`, undefined, undefined)
        const deleteResult = await cloudinary.uploader.destroy(product.image_public_id)

        if (deleteResult.result === 'ok') {
          logApi('DELETE', `/cloudinary/${product.image_public_id}`, 200, undefined)
        } else if (deleteResult.result === 'not found') {
          logApi('DELETE', `/cloudinary/${product.image_public_id}`, 404, undefined)
        } else {
          logError(`Failed to delete product image: ${product.image_public_id}`, deleteResult)
        }
      } catch (deleteError) {
        // Don't fail the deletion if image cleanup fails - just log the error
        logError(`Error deleting product image ${product.image_public_id}`, deleteError)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    })
  } catch (error) {
    logError('Product deletion error', error)
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    )
  }
}
