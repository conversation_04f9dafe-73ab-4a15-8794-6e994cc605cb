import { NextRequest, NextResponse } from 'next/server'

import { supabaseAdmin as supabase } from '@/lib/supabase'
import { roundToCurrency } from '@/utils'

// GET - Fetch single customer debt
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { data: debt, error } = await supabase
      .from('customer_debts')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!debt) {
      return NextResponse.json({ error: 'Debt record not found' }, { status: 404 })
    }

    return NextResponse.json({ debt })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch debt record' },
      { status: 500 }
    )
  }
}

// PUT - Update customer debt
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      product_name,
      product_price,
      quantity,
      debt_date,
      notes
    } = body

    // Validate required fields
    if (!customer_name || !customer_family_name || !product_name || !product_price || !quantity) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate numeric fields
    if (typeof product_price !== 'number' || product_price <= 0) {
      return NextResponse.json(
        { error: 'Product price must be a positive number' },
        { status: 400 }
      )
    }

    if (typeof quantity !== 'number' || quantity <= 0 || !Number.isInteger(quantity)) {
      return NextResponse.json(
        { error: 'Quantity must be a positive integer' },
        { status: 400 }
      )
    }

    // Validate string lengths
    if (customer_name.length < 2 || customer_name.length > 255) {
      return NextResponse.json(
        { error: 'Customer name must be between 2 and 255 characters' },
        { status: 400 }
      )
    }

    if (customer_family_name.length < 2 || customer_family_name.length > 255) {
      return NextResponse.json(
        { error: 'Customer family name must be between 2 and 255 characters' },
        { status: 400 }
      )
    }

    if (product_name.length < 2 || product_name.length > 255) {
      return NextResponse.json(
        { error: 'Product name must be between 2 and 255 characters' },
        { status: 400 }
      )
    }

    // Validate debt date if provided
    if (debt_date && isNaN(Date.parse(debt_date))) {
      return NextResponse.json(
        { error: 'Invalid debt date format' },
        { status: 400 }
      )
    }

    const { data: debt, error } = await supabase
      .from('customer_debts')
      .update({
        customer_name: customer_name.trim(),
        customer_family_name: customer_family_name.trim(),
        product_name: product_name.trim(),
        product_price: roundToCurrency(Number(product_price)),
        quantity: Number(quantity),
        debt_date: debt_date || new Date().toISOString().split('T')[0],
        notes: notes?.trim() || null
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!debt) {
      return NextResponse.json({ error: 'Debt record not found' }, { status: 404 })
    }

    return NextResponse.json({ debt })
  } catch {
    return NextResponse.json(
      { error: 'Failed to update debt record' },
      { status: 500 }
    )
  }
}

// DELETE - Delete customer debt
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    const { error } = await supabase
      .from('customer_debts')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Debt record deleted successfully' })
  } catch {
    return NextResponse.json(
      { error: 'Failed to delete debt record' },
      { status: 500 }
    )
  }
}
