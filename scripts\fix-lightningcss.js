#!/usr/bin/env node

/**
 * Fix LightningCSS Native Binary Issue
 * 
 * This script fixes the common issue where LightningCSS native binaries
 * are not properly linked after npm install, causing the error:
 * "Cannot find module '../lightningcss.win32-x64-msvc.node'"
 * 
 * The script automatically detects the platform and copies the appropriate
 * native binary to the correct location.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getPlatformBinary() {
  const platform = os.platform();
  const arch = os.arch();
  
  // Map Node.js platform/arch to LightningCSS binary names
  const platformMap = {
    'win32': {
      'x64': 'lightningcss-win32-x64-msvc',
      'arm64': 'lightningcss-win32-arm64-msvc'
    },
    'darwin': {
      'x64': 'lightningcss-darwin-x64',
      'arm64': 'lightningcss-darwin-arm64'
    },
    'linux': {
      'x64': 'lightningcss-linux-x64-gnu',
      'arm64': 'lightningcss-linux-arm64-gnu'
    }
  };

  const platformBinaries = platformMap[platform];
  if (!platformBinaries) {
    throw new Error(`Unsupported platform: ${platform}`);
  }

  const binaryName = platformBinaries[arch];
  if (!binaryName) {
    throw new Error(`Unsupported architecture: ${arch} on ${platform}`);
  }

  return binaryName;
}

function fixLightningCSS() {
  try {
    log('🔧 Fixing LightningCSS native binary...', 'cyan');
    
    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    const lightningcssPath = path.join(nodeModulesPath, 'lightningcss');
    
    // Check if lightningcss is installed
    if (!fs.existsSync(lightningcssPath)) {
      log('⚠️  LightningCSS not found, skipping fix', 'yellow');
      return;
    }

    const platformBinary = getPlatformBinary();
    const binaryPackagePath = path.join(nodeModulesPath, platformBinary);
    
    // Check if platform-specific binary package exists
    if (!fs.existsSync(binaryPackagePath)) {
      log(`⚠️  Platform binary package not found: ${platformBinary}`, 'yellow');
      return;
    }

    // Determine the binary file name based on platform
    const platform = os.platform();
    const arch = os.arch();
    let binaryFileName;
    
    if (platform === 'win32') {
      binaryFileName = `lightningcss.win32-${arch}-msvc.node`;
    } else if (platform === 'darwin') {
      binaryFileName = `lightningcss.darwin-${arch}.node`;
    } else if (platform === 'linux') {
      binaryFileName = `lightningcss.linux-${arch}-gnu.node`;
    }

    const sourceBinaryPath = path.join(binaryPackagePath, binaryFileName);
    const targetBinaryPath = path.join(lightningcssPath, binaryFileName);

    // Check if source binary exists
    if (!fs.existsSync(sourceBinaryPath)) {
      log(`⚠️  Source binary not found: ${sourceBinaryPath}`, 'yellow');
      return;
    }

    // Check if target binary already exists
    if (fs.existsSync(targetBinaryPath)) {
      log('✅ LightningCSS binary already exists, no fix needed', 'green');
      return;
    }

    // Copy the binary
    fs.copyFileSync(sourceBinaryPath, targetBinaryPath);
    
    log(`✅ Successfully copied LightningCSS binary: ${binaryFileName}`, 'green');
    log(`   From: ${sourceBinaryPath}`, 'blue');
    log(`   To: ${targetBinaryPath}`, 'blue');
    
  } catch (error) {
    log(`❌ Error fixing LightningCSS: ${error.message}`, 'red');
    
    // Don't fail the installation, just warn
    log('⚠️  Installation will continue, but you may need to fix this manually', 'yellow');
    log('   Run: npm run fix-lightningcss', 'yellow');
  }
}

// Only run if this script is executed directly (not required)
if (require.main === module) {
  fixLightningCSS();
}

module.exports = { fixLightningCSS };
