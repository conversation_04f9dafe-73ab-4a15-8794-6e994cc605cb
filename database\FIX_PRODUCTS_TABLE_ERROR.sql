-- =====================================================
-- FIX FOR "products" TABLE DOES NOT EXIST ERROR
-- =====================================================
-- This script fixes the error: relation "products" does not exist
-- 
-- 🎯 PURPOSE: Ensure all required tables exist before running security fixes
-- 🔧 COMPATIBILITY: Safe to run on any database state
-- 📅 CREATED: 2025-08-30
-- =====================================================

-- First, let's check what tables currently exist
SELECT 
    'Current Tables in Database' as info,
    tablename
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- =====================================================
-- STEP 1: CREATE MISSING CORE TABLES IF THEY DON'T EXIST
-- =====================================================

-- Enable required PostgreSQL extensions first
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create products table if it doesn't exist
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    image_public_id TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    retail_price DECIMAL(10,2) CHECK (retail_price IS NULL OR retail_price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Data integrity constraints
    CONSTRAINT products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT products_category_not_empty CHECK (LENGTH(TRIM(category)) > 0),
    CONSTRAINT products_net_weight_not_empty CHECK (LENGTH(TRIM(net_weight)) > 0)
);

-- Create customers table if it doesn't exist
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT,
    phone_number VARCHAR(20),
    address TEXT,
    birth_date DATE,
    birth_place VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint and data validation
    UNIQUE(customer_name, customer_family_name),
    CONSTRAINT customers_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customers_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customers_phone_format CHECK (phone_number IS NULL OR phone_number ~ '^[0-9+\-\s()]+$'),
    CONSTRAINT customers_birth_date_valid CHECK (birth_date IS NULL OR birth_date <= CURRENT_DATE),
    CONSTRAINT customers_birth_place_not_empty CHECK (birth_place IS NULL OR LENGTH(TRIM(birth_place)) > 0)
);

-- Create customer_debts table if it doesn't exist
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price > 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (product_price * quantity) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Business rule constraints
    CONSTRAINT customer_debts_customer_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_debts_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_debts_product_name_not_empty CHECK (LENGTH(TRIM(product_name)) > 0),
    CONSTRAINT customer_debts_reasonable_price CHECK (product_price <= 100000.00),
    CONSTRAINT customer_debts_reasonable_quantity CHECK (quantity <= 1000),
    CONSTRAINT customer_debts_valid_date CHECK (debt_date >= '2020-01-01' AND debt_date <= CURRENT_DATE + INTERVAL '1 day')
);

-- Create customer_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    responsible_family_member VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Business rule constraints
    CONSTRAINT customer_payments_customer_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_payments_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_payments_reasonable_amount CHECK (payment_amount <= 50000.00),
    CONSTRAINT customer_payments_valid_date CHECK (payment_date >= '2020-01-01' AND payment_date <= CURRENT_DATE + INTERVAL '1 day'),
    CONSTRAINT customer_payments_valid_method CHECK (payment_method IN ('Cash', 'GCash', 'PayMaya', 'Bank Transfer', 'Others'))
);

-- Create audit_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    record_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_by VARCHAR(255),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT audit_log_operation_check CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'))
);

-- =====================================================
-- STEP 2: VERIFY ALL TABLES NOW EXIST
-- =====================================================
SELECT 
    'Tables After Creation' as verification_step,
    tablename,
    CASE 
        WHEN tablename IN ('products', 'customers', 'customer_debts', 'customer_payments', 'audit_log') 
        THEN '✅ Required Table'
        ELSE '📋 Additional Table'
    END as table_status
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- =====================================================
-- STEP 3: ENABLE RLS ON ALL TABLES
-- =====================================================
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 4: CREATE BASIC POLICIES
-- =====================================================
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Enable all operations for application" ON products;
DROP POLICY IF EXISTS "Enable all operations for application" ON customers;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_payments;
DROP POLICY IF EXISTS "Enable all operations for application" ON audit_log;

-- Create application-friendly policies
CREATE POLICY "Enable all operations for application" ON products FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customers FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customer_debts FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customer_payments FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON audit_log FOR ALL USING (true);

-- =====================================================
-- STEP 5: CREATE CUSTOMER BALANCES VIEW
-- =====================================================
-- This view is essential for the Debt Management page
CREATE OR REPLACE VIEW customer_balances
WITH (security_invoker = true) AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    CASE
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0
        THEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0)
        ELSE 0
    END as remaining_balance,
    CASE
        WHEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0) > 0
        THEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0)
        ELSE 0
    END as change_amount,
    last_debt_date,
    last_payment_date,
    debt_count,
    payment_count,
    CASE
        WHEN COALESCE(total_debt, 0) = 0 THEN 'No Debt'
        WHEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0) > 0 THEN 'Overpaid'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) = 0 THEN 'Paid'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0 AND
             COALESCE(total_payments, 0) > 0 THEN 'Outstanding'
        WHEN COALESCE(total_debt, 0) > 0 AND COALESCE(total_payments, 0) = 0 THEN 'Unpaid'
        ELSE 'Unknown'
    END as balance_status,
    ROUND(
        CASE
            WHEN COALESCE(total_debt, 0) > 0
            THEN LEAST((COALESCE(total_payments, 0) / total_debt) * 100, 100)
            ELSE 0
        END, 2
    ) as payment_percentage
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date,
        COUNT(*) as debt_count
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date,
        COUNT(*) as payment_count
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- =====================================================
-- STEP 6: VERIFY CUSTOMER BALANCES VIEW
-- =====================================================
SELECT
    'Customer Balances View Created' as verification_step,
    COUNT(*) as total_customer_records
FROM customer_balances;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '🎉 PRODUCTS TABLE ERROR FIX COMPLETE!';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ TABLES CREATED/VERIFIED:';
    RAISE NOTICE '   📦 products - Product inventory management';
    RAISE NOTICE '   👥 customers - Customer profile management';
    RAISE NOTICE '   💰 customer_debts - Debt tracking';
    RAISE NOTICE '   💳 customer_payments - Payment tracking';
    RAISE NOTICE '   📋 audit_log - Audit trail';
    RAISE NOTICE '   📊 customer_balances - Balance calculations view';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY CONFIGURED:';
    RAISE NOTICE '   ✅ Row Level Security enabled on all tables';
    RAISE NOTICE '   ✅ Application policies created';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 READY FOR DEBT MANAGEMENT!';
    RAISE NOTICE '   • All required tables now exist';
    RAISE NOTICE '   • Customer balances view created';
    RAISE NOTICE '   • No more "relation does not exist" errors';
    RAISE NOTICE '   • Database structure is properly configured';
END $$;

-- =====================================================
-- END OF FIX SCRIPT
-- =====================================================
