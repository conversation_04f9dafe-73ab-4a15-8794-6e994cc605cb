import { v2 as cloudinary } from 'cloudinary'
import { NextRequest, NextResponse } from 'next/server'

import { logError, logApi } from '@/lib/logger'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
})

export async function POST(request: NextRequest) {
  try {
    // Check Cloudinary configuration
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      logError('Missing Cloudinary configuration', undefined, {
        cloudName: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        apiKey: !!process.env.CLOUDINARY_API_KEY,
        apiSecret: !!process.env.CLOUDINARY_API_SECRET
      })
      return NextResponse.json({ error: 'Server configuration error: Missing Cloudinary credentials' }, { status: 500 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const oldPublicId = formData.get('old_public_id') as string | null

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Convert file to buffer for Cloudinary upload
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique public_id for Cloudinary
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const publicId = `customer_profiles/profile_${timestamp}_${randomString}`

    // Upload to Cloudinary
    const uploadResult = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: publicId,
          folder: 'customer_profiles',
          transformation: [
            { width: 400, height: 400, crop: 'fill', gravity: 'face' },
            { quality: 'auto:best', fetch_format: 'auto' },
            { flags: 'progressive' },
            { effect: 'sharpen:100' },
            { effect: 'unsharp_mask:80' }
          ]
        },
        (error, result) => {
          if (error) {
            logError('Cloudinary upload error', error)
            reject(new Error(`Cloudinary upload failed: ${error.message}`))
          } else if (result) {
            resolve(result)
          } else {
            reject(new Error('Upload failed: No result returned from Cloudinary'))
          }
        }
      ).end(buffer)
    })

    const result = uploadResult as { secure_url: string; public_id: string }

    // Delete old image from Cloudinary if it exists
    if (oldPublicId) {
      try {
        logApi('DELETE', `/cloudinary/${oldPublicId}`, undefined, undefined)
        const deleteResult = await cloudinary.uploader.destroy(oldPublicId)

        if (deleteResult.result === 'ok') {
          logApi('DELETE', `/cloudinary/${oldPublicId}`, 200, undefined)
        } else if (deleteResult.result === 'not found') {
          logApi('DELETE', `/cloudinary/${oldPublicId}`, 404, undefined)
        } else {
          logError(`Failed to delete old profile picture: ${oldPublicId}`, deleteResult)
        }
      } catch (deleteError) {
        // Don't fail the upload if deletion fails - just log the error
        logError(`Error deleting old profile picture ${oldPublicId}`, deleteError)
      }
    }

    // Add cache-busting parameter to ensure fresh image loads
    const cacheBustUrl = `${result.secure_url}?cache_bust=${Date.now()}`

    return NextResponse.json({
      success: true,
      url: cacheBustUrl,
      public_id: result.public_id,
      filename: result.public_id
    })

  } catch (error) {
    logError('Error uploading file to Cloudinary', error)
    return NextResponse.json(
      { error: 'Failed to upload file to cloud storage' },
      { status: 500 }
    )
  }
}

// DELETE - Remove profile picture from Cloudinary
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 })
    }

    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId)

    if (result.result === 'ok') {
      return NextResponse.json({
        success: true,
        message: 'Profile picture deleted successfully from cloud storage'
      })
    } else {
      return NextResponse.json({
        error: 'Failed to delete image from cloud storage'
      }, { status: 400 })
    }

  } catch (error) {
    logError('Error deleting file from Cloudinary', error)
    return NextResponse.json(
      { error: 'Failed to delete file from cloud storage' },
      { status: 500 }
    )
  }
}
