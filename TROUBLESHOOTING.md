# Troubleshooting Guide

## Common Issues and Solutions

### 1. Request Timeout Errors (AbortError)

**Symptoms:**
- "Request timed out after 30 seconds" error
- `AbortError: signal is aborted without reason`
- Products not loading

**Causes:**
- Slow internet connection
- Supabase server performance issues
- Network connectivity problems
- Database query performance issues

**Solutions:**

#### Immediate Fixes:
1. **Check Internet Connection**
   - Test your internet speed
   - Try accessing other websites
   - Disable VPN/proxy temporarily

2. **Use Connection Test**
   - Click "Test Connection" button in error screen
   - Review the diagnostic results
   - Follow the recommendations provided

3. **Clear Browser Cache**
   ```bash
   # In browser dev tools (F12)
   # Go to Application > Storage > Clear storage
   # Or use Ctrl+Shift+Delete
   ```

4. **Restart Development Server**
   ```bash
   # Stop the server (Ctrl+C)
   npm run dev
   ```

#### Advanced Fixes:

1. **Check Supabase Status**
   - Visit [Supabase Status Page](https://status.supabase.com/)
   - Check if there are ongoing issues

2. **Optimize Database Queries**
   - Check Supabase dashboard for slow queries
   - Consider adding database indexes

3. **Environment Variables**
   ```bash
   # Verify your .env.local file has correct values
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

### 2. Compilation/Build Issues

**Symptoms:**
- Long compilation times (20+ seconds)
- Webpack cache errors
- Build failures

**Solutions:**

1. **Clear Next.js Cache**
   ```bash
   npm run clean
   # Or manually:
   rm -rf .next
   rm -rf node_modules/.cache
   ```

2. **Update Dependencies**
   ```bash
   npm update
   ```

3. **Check Node.js Version**
   ```bash
   node --version
   # Should be >= 18.0.0
   ```

### 3. Performance Issues

**Symptoms:**
- Slow page loads
- High memory usage
- Laggy UI interactions

**Solutions:**

1. **Enable Performance Monitoring**
   - Open browser dev tools (F12)
   - Check console for performance reports
   - Look for slow API calls

2. **Optimize Images**
   - Use WebP format when possible
   - Compress images before upload
   - Use appropriate image sizes

3. **Database Optimization**
   - Limit query results with pagination
   - Add indexes to frequently queried columns
   - Use connection pooling

### 4. API Response Structure Issues

**Symptoms:**
- "Invalid API response structure" error
- Empty product lists despite data in database

**Solutions:**

1. **Check API Response Format**
   ```javascript
   // Expected format:
   {
     "success": true,
     "data": {
       "products": [...],
       "total": 24
     }
   }
   ```

2. **Verify Database Schema**
   - Ensure products table exists
   - Check column names match expected format
   - Verify data types are correct

### 5. Environment Configuration Issues

**Symptoms:**
- "Missing required environment variables" errors
- Authentication failures
- Image upload failures

**Solutions:**

1. **Validate Environment Setup**
   ```bash
   npm run validate
   ```

2. **Check Required Variables**
   ```bash
   # Required in .env.local:
   NEXT_PUBLIC_SUPABASE_URL=
   NEXT_PUBLIC_SUPABASE_ANON_KEY=
   NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=
   CLOUDINARY_API_KEY=
   CLOUDINARY_API_SECRET=
   NEXTAUTH_SECRET=
   ```

3. **Test Individual Services**
   ```bash
   npm run test-cloudinary
   ```

## Performance Optimization Tips

### 1. Database Optimization
- Use pagination for large datasets
- Add indexes on frequently queried columns
- Optimize query complexity
- Use connection pooling

### 2. Frontend Optimization
- Implement proper loading states
- Use debounced API calls
- Optimize image sizes and formats
- Minimize bundle size

### 3. Network Optimization
- Use CDN for static assets
- Enable compression
- Implement proper caching strategies
- Use HTTP/2 when possible

## Monitoring and Debugging

### 1. Browser Dev Tools
- Network tab: Check API response times
- Console: Look for error messages and performance reports
- Performance tab: Analyze page load times

### 2. Server Logs
- Check terminal output for API errors
- Monitor database query performance
- Watch for memory usage patterns

### 3. Performance Metrics
- Use built-in performance monitoring
- Track API response times
- Monitor error rates and patterns

## Getting Help

If you're still experiencing issues:

1. **Check the Console**
   - Open browser dev tools (F12)
   - Look for error messages in Console tab
   - Check Network tab for failed requests

2. **Run Diagnostics**
   - Use the "Test Connection" feature
   - Run `npm run validate`
   - Check performance reports in console

3. **Gather Information**
   - Browser version and OS
   - Error messages (full text)
   - Steps to reproduce the issue
   - Network conditions (speed, stability)

4. **Common Solutions to Try First**
   - Restart the development server
   - Clear browser cache
   - Check internet connection
   - Verify environment variables
