import { NextRequest, NextResponse } from 'next/server'

import { supabase } from '@/lib/supabase'
import { logError, logApi } from '@/lib/logger'

// Helper function to get user identifier (IP address for now)
function getUserIdentifier(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  let ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown'
  
  // Fallback to a session-based identifier if IP is not available
  if (ip === 'unknown' || ip === '::1' || ip === '127.0.0.1') {
    // For development/localhost, use a combination of user agent and timestamp
    const userAgent = request.headers.get('user-agent') || 'unknown'
    ip = `local_${Buffer.from(userAgent).toString('base64').substring(0, 10)}`
  }
  
  return ip
}

// POST - Toggle like for a photo
export async function POST(request: NextRequest) {
  try {
    const { photoId } = await request.json()

    if (!photoId) {
      return NextResponse.json(
        { error: 'Photo ID is required' },
        { status: 400 }
      )
    }

    const userIdentifier = getUserIdentifier(request)
    
    logApi('POST', '/api/gallery/like', undefined, undefined)

    // Check if photo exists
    const { data: photo, error: photoError } = await supabase
      .from('gallery_photos')
      .select('id, likes_count')
      .eq('id', photoId)
      .single()

    if (photoError || !photo) {
      return NextResponse.json(
        { error: 'Photo not found' },
        { status: 404 }
      )
    }

    // Check if user has already liked this photo
    const { data: existingLike, error: likeCheckError } = await supabase
      .from('gallery_likes')
      .select('id')
      .eq('photo_id', photoId)
      .eq('user_identifier', userIdentifier)
      .single()

    if (likeCheckError && likeCheckError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected if no like exists
      logError('Like check error', likeCheckError)
      return NextResponse.json(
        { error: 'Failed to check like status' },
        { status: 500 }
      )
    }

    let isLiked = false
    let newLikesCount = photo.likes_count

    if (existingLike) {
      // Unlike - remove the like
      const { error: unlikeError } = await supabase
        .from('gallery_likes')
        .delete()
        .eq('photo_id', photoId)
        .eq('user_identifier', userIdentifier)

      if (unlikeError) {
        logError('Unlike error', unlikeError)
        return NextResponse.json(
          { error: 'Failed to unlike photo' },
          { status: 500 }
        )
      }

      newLikesCount = Math.max(0, photo.likes_count - 1)
      isLiked = false
    } else {
      // Like - add the like
      const { error: likeError } = await supabase
        .from('gallery_likes')
        .insert({
          photo_id: photoId,
          user_identifier: userIdentifier
        })

      if (likeError) {
        logError('Like error', likeError)
        return NextResponse.json(
          { error: 'Failed to like photo' },
          { status: 500 }
        )
      }

      newLikesCount = photo.likes_count + 1
      isLiked = true
    }

    // Update the likes count in the photo record
    const { error: updateError } = await supabase
      .from('gallery_photos')
      .update({ likes_count: newLikesCount })
      .eq('id', photoId)

    if (updateError) {
      logError('Likes count update error', updateError)
      return NextResponse.json(
        { error: 'Failed to update likes count' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      isLiked,
      likesCount: newLikesCount
    })
  } catch (error) {
    logError('Gallery like API error', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - Get like status for a photo
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const photoId = searchParams.get('photoId')

    if (!photoId) {
      return NextResponse.json(
        { error: 'Photo ID is required' },
        { status: 400 }
      )
    }

    const userIdentifier = getUserIdentifier(request)
    
    logApi('GET', '/api/gallery/like', undefined, undefined)

    // Check if user has liked this photo
    const { data: existingLike, error: likeCheckError } = await supabase
      .from('gallery_likes')
      .select('id')
      .eq('photo_id', photoId)
      .eq('user_identifier', userIdentifier)
      .single()

    if (likeCheckError && likeCheckError.code !== 'PGRST116') {
      logError('Like status check error', likeCheckError)
      return NextResponse.json(
        { error: 'Failed to check like status' },
        { status: 500 }
      )
    }

    // Get current likes count
    const { data: photo, error: photoError } = await supabase
      .from('gallery_photos')
      .select('likes_count')
      .eq('id', photoId)
      .single()

    if (photoError) {
      logError('Photo fetch error', photoError)
      return NextResponse.json(
        { error: 'Photo not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      isLiked: !!existingLike,
      likesCount: photo.likes_count
    })
  } catch (error) {
    logError('Gallery like status API error', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
